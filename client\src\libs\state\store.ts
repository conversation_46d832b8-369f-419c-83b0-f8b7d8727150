import { configureStore, combineReducers } from '@reduxjs/toolkit'
import { setupListeners } from '@reduxjs/toolkit/query/react'
import storage from 'redux-persist/lib/storage'
import { persistReducer, persistStore } from 'redux-persist'

import authReducer from '@/libs/features/auth/authSlice'
import { authApi } from '@/services/M1/auth'
import { userApi } from '@/services/M1/user'
import { qrApi } from '@/services/M1/Qr/qr'
import { affiliateApi } from '@/services/M1/affiliate'
import { userManagementApi } from '@/services/M1/UserManagement/userManagement'
import { productManagementApi } from '@/services/M2/ProductManagement/productManagement'
import { categoryManagementApi } from '@/services/M2/ProductManagement/categoryManagement'
import { chatBotApi } from '@/services/Chatbot/supportChatbot'
import { cartApi } from '@/services/M2/CartManagement/cart'
import { orderApi } from '@/services/M2/Orders/order'
import { orderManagementApi } from '@/services/M2/OrderManagement/orderManagement'
import { productReviewApi } from '@/services/M2/ProductManagement/productReview'
import { inventoryManagementApi } from '@/services/M2/InventoryManagement/inventoryManagement'

// ✅ Persist chỉ cho auth
const authPersistConfig = {
  key: 'auth',
  storage,
}

const rootReducer = combineReducers({
  auth: persistReducer(authPersistConfig, authReducer), // ✅ chỉ persist cái này
  [authApi.reducerPath]: authApi.reducer,
  [userApi.reducerPath]: userApi.reducer,
  [qrApi.reducerPath]: qrApi.reducer,
  [affiliateApi.reducerPath]: affiliateApi.reducer,
  [userManagementApi.reducerPath]: userManagementApi.reducer,
  [productManagementApi.reducerPath]: productManagementApi.reducer, // ✅ Thêm reducer này
  [categoryManagementApi.reducerPath]: categoryManagementApi.reducer, // ✅ Thêm reducer cho category
  [chatBotApi.reducerPath]: chatBotApi.reducer, // ✅ Thêm reducer cho category
  [cartApi.reducerPath]: cartApi.reducer, // ✅ Thêm reducer cho cartApi
  [orderApi.reducerPath]: orderApi.reducer, // ✅ Thêm reducer cho orderApi
  [orderManagementApi.reducerPath]: orderManagementApi.reducer, // ✅ Thêm reducer cho orderManagementApi
  [productReviewApi.reducerPath]: productReviewApi.reducer, // ✅ Thêm reducer cho productReviewApi
  [inventoryManagementApi.reducerPath]: inventoryManagementApi.reducer, // ✅ Thêm reducer cho inventoryManagementApi
})

// ❌ KHÔNG wrap rootReducer bằng persistReducer nữa!
export const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }).concat(
      authApi.middleware,
      userApi.middleware,
      qrApi.middleware,
      affiliateApi.middleware,
      userManagementApi.middleware,
      productManagementApi.middleware, // ✅ Thêm middleware này
      categoryManagementApi.middleware, // ✅ Thêm middleware cho category
      chatBotApi.middleware, // ✅ Thêm middleware cho category
      cartApi.middleware, // ✅ Thêm middleware cho cartApi
      orderApi.middleware, // ✅ Thêm middleware cho orderApi
      orderManagementApi.middleware, // ✅ Thêm middleware cho orderManagementApi
      productReviewApi.middleware, // ✅ Thêm middleware cho productReviewApi
      inventoryManagementApi.middleware // ✅ Thêm middleware cho inventoryManagementApi
    ),
})

export const persistor = persistStore(store)

setupListeners(store.dispatch)

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
