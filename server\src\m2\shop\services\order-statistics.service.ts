import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Order } from '../entities/order.entity';
import { OrderItem } from '../entities/order_item.entity';
import { Product } from '../entities/products.entity';
import {
  OrderStatisticsDto,
  OrderTrendDto,
  PaymentMethodStatsDto,
  RevenueDetailsDto,
  CancelledOrderListDto,
  OrderStatisticsQueryDto,
} from '../dto/order-statistics.dto';

@Injectable()
export class OrderStatisticsService {
  constructor(
    @InjectRepository(Order)
    private readonly orderRepo: Repository<Order>,
    @InjectRepository(OrderItem)
    private readonly orderItemRepo: Repository<OrderItem>,
    @InjectRepository(Product)
    private readonly productRepo: Repository<Product>,
  ) {}

  async getStatistics(
    query: OrderStatisticsQueryDto & { shopId?: string },
  ): Promise<OrderStatisticsDto> {
    // Filter by date range
    const where: any = {};
    if (query.from && query.to) {
      where.created_at = {
        $gte: new Date(query.from),
        $lte: new Date(query.to),
      };
    }
    // If shopId provided, filter orders by products.user_id
    let shopUserId = query.shopId;
    let orders: Order[];
    if (shopUserId) {
      // Get all product ids of this shop
      const productIds = (await this.productRepo.find({ where: { user_id: shopUserId } })).map(
        (p) => p.id,
      );
      // Get all order items with these product ids
      const orderItemIds = (
        await this.orderItemRepo.find({ where: { product_id: In(productIds) } })
      ).map((oi) => oi.order_id);
      // Get all orders with those ids
      orders = await this.orderRepo.findByIds(orderItemIds);
    } else {
      orders = await this.orderRepo.find({ where });
    }
    // Calculate statistics
    const totalOrders = orders.length;
    const completedOrders = orders.filter((o) => o.status?.code === 'completed').length;
    const cancelledOrders = orders.filter((o) => o.status?.code === 'cancelled').length;
    const totalRevenue = orders.reduce((sum, o) => sum + parseFloat(o.total_price || '0'), 0);
    const actualRevenue = totalRevenue; // TODO: subtract refunds if needed
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
    // TODO: previousPeriodComparison logic
    const previousPeriodComparison = 0;
    return {
      totalOrders,
      completedOrders,
      cancelledOrders,
      totalRevenue,
      actualRevenue,
      averageOrderValue,
      previousPeriodComparison,
    };
  }

  async getTrend(query: OrderStatisticsQueryDto): Promise<OrderTrendDto[]> {
    // TODO: Implement actual logic
    return [
      {
        date: '2025-07-01',
        orders: 10,
        revenue: 1200000,
        completedOrders: 8,
        cancelledOrders: 2,
      },
    ];
  }

  async getPaymentMethods(query: OrderStatisticsQueryDto): Promise<PaymentMethodStatsDto[]> {
    // TODO: Implement actual logic
    return [
      { method: 'COD', value: 50, percentage: 50, color: '#1890ff' },
      { method: 'Bank', value: 30, percentage: 30, color: '#52c41a' },
      { method: 'E-wallet', value: 20, percentage: 20, color: '#faad14' },
    ];
  }

  async getRevenueDetails(query: OrderStatisticsQueryDto): Promise<RevenueDetailsDto> {
    // TODO: Implement actual logic
    return { actualRevenue: 9500000, totalRevenue: ******** };
  }

  async getCancelledOrders(query: OrderStatisticsQueryDto): Promise<CancelledOrderListDto[]> {
    // TODO: Implement actual logic
    return [{ id: 'order1', cancelledAt: '2025-07-10', amount: 120000, customer: 'Nguyen Van A' }];
  }
}
