import { Status } from '../entities/status.entity';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  Req,
  UseGuards,
  NotFoundException,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { OrderService } from '../services/order.service';
import { PaginationRequest } from '../dto/pagination-request.dto';
import { UpdateOrderStatusDto } from '../dto/update-order-status.dto';

// L<PERSON>y danh sách các yêu cầu hủy đơn cho admin

@ApiTags('Admin Orders')
@Controller('orders-management')
@ApiBearerAuth('access-token')
@UseGuards(AuthGuard('jwt'))
export class AdminOrderController {
  constructor(private readonly orderService: OrderService) {}

  // L<PERSON>y danh sách tất cả đơn hàng cho admin (pagination, search, filter by status)
  @Get('/admin/order')
  @ApiOperation({ summary: 'Admin: <PERSON><PERSON><PERSON> kiếm, phân trang đơn hàng' })
  @ApiResponse({ status: 200 })
  async searchOrders(@Query() query: PaginationRequest) {
    return this.orderService.getAllOrdersForAdmin(query);
  }

  // Lấy chi tiết đơn hàng cho admin
  @Get('/admin/order/:id')
  @ApiOperation({ summary: 'Admin: Lấy chi tiết đơn hàng theo id' })
  @ApiResponse({ status: 200 })
  async getOrder(@Param('id') id: string) {
    return this.orderService.getOrderDetailForAdmin(id);
  }

  // Lấy danh sách đơn hàng của seller
  @Get('/seller')
  @ApiOperation({ summary: 'Seller: Tìm kiếm, phân trang đơn hàng của seller/người bán hiện tại' })
  @ApiResponse({ status: 200 })
  async searchOrdersBySeller(@Req() req: any, @Query() query: PaginationRequest) {
    const shopId = req.user?.userId;
    return this.orderService.getAllOrdersForShop(shopId, query);
  }

  // Lấy danh sách các yêu cầu hủy đơn cho seller/shop (đặt trước endpoint động)
  @Get('/seller/cancel-requests')
  @ApiOperation({ summary: 'Seller: Danh sách các yêu cầu hủy đơn của seller/người bán' })
  @ApiResponse({ status: 200 })
  async getCancelRequestsForSeller(@Req() req: any, @Query() query: PaginationRequest) {
    const shopId = req.user?.userId;
    return this.orderService.getCancelRequestsForShop(shopId, query);
  }

  // Lấy chi tiết đơn hàng của seller/người bán hiện tại
  @Get('/seller/:order_id')
  @ApiOperation({ summary: 'Seller: Lấy chi tiết đơn hàng của seller/người bán hiện tại' })
  @ApiResponse({ status: 200 })
  async getOrderOfSeller(@Req() req: any, @Param('order_id') orderId: string) {
    const shopId = req.user?.userId;
    return this.orderService.getOrderDetailForShop(shopId, orderId);
  }

  // API cập nhật trạng thái đơn hàng
  @Post(':id/status')
  @ApiOperation({ summary: 'Admin: Cập nhật trạng thái đơn hàng' })
  @ApiResponse({ status: 200 })
  async updateOrderStatus(
    @Param('id') id: string,
    @Body() body: UpdateOrderStatusDto,
    @Req() req: any,
  ) {
    const userId = req.user?.userId;
    return this.orderService.updateOrderStatus(id, body.status_id, userId);
  }

  @Get('/admin/cancel-requests')
  @ApiOperation({ summary: 'Admin: Danh sách các yêu cầu hủy đơn' })
  @ApiResponse({ status: 200 })
  async getCancelRequestsForAdmin(@Query() query: PaginationRequest) {
    return this.orderService.getCancelRequestsForAdmin(query);
  }

  // Lấy danh sách các yêu cầu hủy đơn cho seller/shop
  // API duyệt yêu cầu hủy đơn (admin hoặc seller)
  @Post(':id/accept-cancel-request')
  @ApiOperation({ summary: 'Duyệt yêu cầu hủy đơn (admin/seller)' })
  @ApiResponse({ status: 200 })
  async acceptCancelRequest(
    @Param('id') orderId: string,
    @Req() req: any,
    @Body('note') note?: string,
  ) {
    const userId = req.user?.userId;
    return this.orderService.acceptCancelRequest(orderId, userId, note);
  }
  // Có thể bổ sung các API cập nhật trạng thái, xác nhận, huỷ đơn... tại đây
}
