// File: layouts/ProtectedLayout.tsx

import { useState } from 'react'
import { Outlet, useNavigate, useLocation } from 'react-router-dom'
import { Layout, Button, Menu } from 'antd'
import { AiOutlineMenuFold, AiOutlineMenuUnfold } from 'react-icons/ai'
import { useSelector } from 'react-redux'

import logo from '@/assets/images/logo.png'
import FooterSystem from './SystemLayout/Footer'
import HeaderSystem from './SystemLayout/Header'
import { commonSidebarItems, permissionSidebarItems } from '@/constants/sidebarItems'
import type { RootState } from '@/libs/state/store'
import { checkPermission } from '@/helpers/permission'

const { Content } = Layout

const drawerWidthOpen = 200
const drawerWidthCollapsed = 80

export type ProtectedLayoutProps = {
  children: React.ReactNode
}

const ProtectedLayout = () => {
  const [collapsed, setCollapsed] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()

  // ✅ Lấy permission từ Redux
  const authUser = useSelector((state: RootState) => state.auth)
  const userPermissions = authUser?.permissions || []

  const toggleDrawer = () => setCollapsed(!collapsed)

  // ✅ Gộp menu item theo permission
  const menuItems = [
    ...commonSidebarItems,
    ...permissionSidebarItems.filter(
      (item) =>
        !item.requiredPermissions || checkPermission(userPermissions, item.requiredPermissions, authUser?.role?.role_code)
    ),
  ].map((item) => ({
    key: item.link,
    icon: item.icon,
    label: item.name,
    onClick: () => navigate(item.link),
  }))

  return (
    <Layout style={{ minHeight: '100vh', transition: 'all 0.3s' }}>
      {/* Sidebar Drawer */}
      <div
        style={{
          width: collapsed ? drawerWidthCollapsed : drawerWidthOpen,
          backgroundColor: '#fff',
          position: 'fixed',
          height: '100vh',
          borderRight: '1px solid #e5e5e5',
          transition: 'width 0.3s',
          zIndex: 10,
        }}
      >
        {/* Logo & Toggle */}
        <div
          style={{
            height: 64,
            padding: 16,
            display: 'flex',
            alignItems: 'center',
            justifyContent: collapsed ? 'center' : 'space-between',
            borderBottom: '1px solid #f0f0f0',
          }}
        >
          <img
            src={logo}
            alt="Logo"
            style={{
              height: 38,
              objectFit: 'contain',
              opacity: collapsed ? 0 : 1,
              transition: 'opacity 0.3s',
            }}
          />
          <Button
            type="text"
            onClick={toggleDrawer}
            icon={collapsed ? <AiOutlineMenuUnfold size={18} /> : <AiOutlineMenuFold size={18} />}
          />
        </div>

        {/* Menu items */}
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          style={{ borderRight: 0 }}
          inlineCollapsed={collapsed}
          items={menuItems}
        />
      </div>

      {/* Main layout content */}
      <Layout
        style={{
          marginLeft: collapsed ? drawerWidthCollapsed : drawerWidthOpen,
          transition: 'margin-left 0.3s',
        }}
      >
        <HeaderSystem isToggleNavbar />
        <Content style={contentStyle}>
          <Outlet />
        </Content>
        <FooterSystem />
      </Layout>
    </Layout>
  )
}

export default ProtectedLayout

const contentStyle: React.CSSProperties = {
  padding: 24,
  backgroundColor: '#f0f2f5',
  minHeight: 'calc(100vh - 128px)',
  transition: 'all 0.3s',
}
