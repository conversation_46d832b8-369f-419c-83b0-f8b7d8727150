import React, { useEffect, useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Form, Input, Button, Card, Row, Col, Divider, Spin } from 'antd';
import styles from './Orders.module.scss';
import { useCreateOrderHook } from '@/hooks/orders/useOrder';
import { useGetCartHook } from '@/hooks/cart/useCart';
import type { CreateOrderRequest } from '@/services/M2/Orders/order';
import { useToast } from '@/components/Toast';
import { useGetProductDetailQuery } from '@/services/M2/ProductManagement/productManagement';
import logo from '@/assets/images/logo.png';

const Order: React.FC = () => {
  const [form] = Form.useForm();
  const location = useLocation();
  const navigate = useNavigate();
  const { showError } = useToast();
  const createOrder = useCreateOrderHook();
  
  // ✅ ĐƯA TẤT CẢ HOOKS LÊN TRÊN CÙNG
  const { data: cartData, isLoading: isLoadingCart } = useGetCartHook();

  // Lấy dữ liệu từ trang Cart
  const { items: selectedItems, totals } = location.state || { items: [], totals: {} };

  // ✅ LUÔN GỌI useMemo, KHÔNG BAO GIỜ CONDITIONAL
  const enrichedItems = useMemo(() => {
    if (!selectedItems || selectedItems.length === 0 || !cartData) return [];
    
    const cartItems = Array.isArray(cartData) ? cartData : cartData.data || [];
    
    return selectedItems.map((selectedItem: any) => {
      const cartItem = cartItems.find((cartItem: any) => 
        cartItem.version_id === selectedItem.version_id
      );
      
      return {
        ...selectedItem,
        product: cartItem?.product || {},
        version: cartItem?.version || {},
        thumbnail_url: cartItem?.product?.thumbnail_url || selectedItem.thumbnail_url,
        name: cartItem?.product?.name || selectedItem.name,
      };
    });
  }, [selectedItems, cartData]);

  // ✅ EFFECT ĐỂ REDIRECT THAY VÌ RETURN EARLY
  useEffect(() => {
    if (!selectedItems || selectedItems.length === 0) {
      showError('Không có sản phẩm để thanh toán. Vui lòng quay lại giỏ hàng.');
      navigate('/gio-hang');
    }
  }, [selectedItems, navigate, showError]);

  const onFinish = async (values: any) => {
    try {
      // Validate thêm trước khi submit
      if (!enrichedItems || enrichedItems.length === 0) {
        showError('Không có sản phẩm để thanh toán.');
        return;
      }

      const orderItems = enrichedItems.map((item: any) => ({
        product_id: item.product_id,
        version_id: item.version_id,
        quantity: item.quantity,
        unit_price: item.unit_price,
        selected_options: {},
      }));

      const orderPayload: CreateOrderRequest = {
        items: orderItems,
        shipping_address: values.shipping_address.trim(),
        receiver_name: values.receiver_name.trim(),
        receiver_phone: values.receiver_phone.trim(),
        note: values.note?.trim() || '',
        source: 'buy_now',
        guest_phone: undefined,
        guest_email: undefined,
        ref_code: undefined,
        is_guest: false,
      };

      const success = await createOrder(orderPayload);

      if (success) {
        navigate('/gio-hang/don-hang-cua-toi');
      }
    } catch (error) {
      showError('Có lỗi xảy ra khi đặt hàng. Vui lòng thử lại.');
    }
  };

  const onFinishFailed = (errorInfo: any) => {
    showError('Vui lòng kiểm tra lại thông tin đã nhập.');
  };

  // ✅ COMPONENT CON VỚI HOOKS RIÊNG
  const ProductDetailSummary = ({ item }: { item: any }) => {
    const { data: detail, isLoading } = useGetProductDetailQuery(item.product_id, { 
      skip: !item.product_id 
    });
    
    return (
      <div className={styles.summaryItem}>
        {isLoading ? (
          <Spin size="small" />
        ) : detail ? (
          <>
            <img 
              src={detail.thumbnail_url || item.thumbnail_url || logo} 
              alt={detail.name || item.name} 
              className={styles.itemImage}
              onError={(e) => {
                (e.target as HTMLImageElement).src = '/placeholder-image.png';
              }}
            />
            <div className={styles.itemDetails}>
              <span className={styles.itemName}>{detail.name}</span>
              {detail.description && (
                <span className={styles.itemDesc}>{detail.description}</span>
              )}
              {item.version?.name && (
                <span className={styles.itemVariant}>Phân loại: {item.version.name}</span>
              )}
              <span className={styles.itemQuantity}>SL: {item.quantity}</span>
            </div>
            <span className={styles.itemPrice}>
              {(item.unit_price * item.quantity).toLocaleString()}đ
            </span>
          </>
        ) : (
          <>
            <img 
              src={item.thumbnail_url || logo} 
              alt={item.name} 
              className={styles.itemImage}
              onError={(e) => {
                (e.target as HTMLImageElement).src = '/placeholder-image.png';
              }}
            />
            <div className={styles.itemDetails}>
              <span className={styles.itemName}>{item.name}</span>
              {item.version?.name && (
                <span className={styles.itemVariant}>Phân loại: {item.version.name}</span>
              )}
              <span className={styles.itemQuantity}>SL: {item.quantity}</span>
            </div>
            <span className={styles.itemPrice}>
              {(item.unit_price * item.quantity).toLocaleString()}đ
            </span>
          </>
        )}
      </div>
    );
  };

  // ✅ CONDITIONAL RENDERING THAY VÌ RETURN EARLY
  if (isLoadingCart) {
    return (
      <div className={styles.checkoutContainer}>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
          <p>Đang tải thông tin sản phẩm...</p>
        </div>
      </div>
    );
  }

  if (!selectedItems || selectedItems.length === 0) {
    return (
      <div className={styles.checkoutContainer}>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
          <p>Đang chuyển hướng...</p>
        </div>
      </div>
    );
  }

  if (!enrichedItems || enrichedItems.length === 0) {
    return (
      <div className={styles.checkoutContainer}>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          Không có sản phẩm để thanh toán.
        </div>
      </div>
    );
  }

  return (
    <div className={styles.checkoutContainer}>
      <h1>Thanh toán đơn hàng</h1>
      <Row gutter={[24, 24]}>
        {/* Cột trái: Thông tin giao hàng */}
        <Col xs={24} md={14}>
          <Card title="Thông tin giao hàng">
            <Form form={form} layout="vertical" onFinish={onFinish} onFinishFailed={onFinishFailed}>
              <Form.Item
                name="receiver_name"
                label="Tên người nhận"
                rules={[
                  { required: true, message: 'Vui lòng nhập tên người nhận!' },
                  { min: 2, message: 'Tên phải có ít nhất 2 ký tự!' },
                  { max: 50, message: 'Tên không được quá 50 ký tự!' },
                  { 
                    pattern: /^[a-zA-ZÀ-ỹ\s]+$/, 
                    message: 'Tên chỉ được chứa chữ cái và khoảng trắng!' 
                  }
                ]}
              >
                <Input placeholder="Nguyễn Văn A" />
              </Form.Item>
              <Form.Item
                name="receiver_phone"
                label="Số điện thoại"
                rules={[
                  { required: true, message: 'Vui lòng nhập số điện thoại!' },
                  { 
                    pattern: /^(0|\+84)[3|5|7|8|9][0-9]{8}$/, 
                    message: 'Số điện thoại không hợp lệ! (VD: 0987654321)' 
                  }
                ]}
              >
                <Input placeholder="0987654321" />
              </Form.Item>
              <Form.Item
                name="shipping_address"
                label="Địa chỉ giao hàng"
                rules={[
                  { required: true, message: 'Vui lòng nhập địa chỉ!' },
                  { min: 10, message: 'Địa chỉ phải có ít nhất 10 ký tự!' },
                  { max: 200, message: 'Địa chỉ không được quá 200 ký tự!' }
                ]}
              >
                <Input.TextArea 
                  rows={3} 
                  placeholder="Số nhà, tên đường, phường/xã, quận/huyện, tỉnh/thành phố" 
                />
              </Form.Item>
              <Form.Item 
                name="note" 
                label="Ghi chú cho đơn hàng (tùy chọn)"
                rules={[
                  { max: 500, message: 'Ghi chú không được quá 500 ký tự!' }
                ]}
              >
                <Input.TextArea 
                  rows={2} 
                  placeholder="Giao hàng trong giờ hành chính..." 
                />
              </Form.Item>
              <Form.Item>
                <Button type="primary" htmlType="submit" block size="large">
                  Xác nhận và Đặt hàng
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* Cột phải: Tóm tắt đơn hàng */}
        <Col xs={24} md={10}>
          <Card title="Tóm tắt đơn hàng">
            <div className={styles.orderSummary}>
              {enrichedItems.map((item: any) => (
                <ProductDetailSummary key={item.version_id} item={item} />
              ))}
              <Divider />
              <div className={styles.summaryRow}>
                <span>Tạm tính</span>
                <span>{totals.subtotal?.toLocaleString()}đ</span>
              </div>
              <div className={styles.summaryRow}>
                <span>Phí vận chuyển</span>
                <span>{totals.shipping?.toLocaleString()}đ</span>
              </div>
              {totals.discount > 0 && (
                <div className={`${styles.summaryRow} ${styles.discount}`}>
                  <span>Giảm giá</span>
                  <span>-{totals.discount?.toLocaleString()}đ</span>
                </div>
              )}
              <Divider />
              <div className={`${styles.summaryRow} ${styles.total}`}>
                <span>Tổng cộng</span>
                <span>{totals.total?.toLocaleString()}đ</span>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Order;
