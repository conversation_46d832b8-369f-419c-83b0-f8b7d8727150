import { createApi } from '@reduxjs/toolkit/query/react'
import { axiosBaseQuery } from '../../../libs/axios/axiosBase'

const baseUrl = 'http://localhost:8080'

export const productManagementApi = createApi({
  reducerPath: 'productManagementApi',
  baseQuery: axiosBaseQuery({ baseUrl }),
  tagTypes: ['Categories', 'Products', 'ProductImages', 'Favorites', 'Inventory', 'ProductStats'],

  endpoints: (build) => ({
    // ✅ Tạo sản phẩm mới
    createProduct: build.mutation<CreateProductResponse, CreateProductRequest>({
      query: (data: CreateProductRequest) => ({
        url: '/products',
        method: 'POST',
        data: data,
        authRequired: true,
      }),
      invalidatesTags: ['Products'],
    }),

    // ✅ Lấy danh sách categories
    getCategories: build.query<GetCategoriesResponse, GetCategoriesParams>({
      query: (params: GetCategoriesParams) => ({
        url: '/categories',
        method: 'GET',
        params: params,
        authOptional: true, // Gửi token nếu có, không bắt buộc
      }),
      providesTags: ['Categories'],
    }),

    getPublicCategories: build.query<GetCategoriesResponse, GetCategoriesParams>({
      query: (params: GetCategoriesParams) => ({
        url: '/categories/public',
        method: 'GET',
        params: params,
        authOptional: false, // Gửi token nếu có, không bắt buộc
      }),
      providesTags: ['Categories'],
    }),

    // ✅ Upload ảnh sản phẩm
    uploadProductImages: build.mutation<UploadImagesResponse, FormData>({
      query: (formData: FormData) => ({
        url: '/product-images/upload',
        method: 'POST',
        data: formData,
        authRequired: true,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }),
      invalidatesTags: ['ProductImages'],
    }),

    // ✅ Lấy danh sách sản phẩm (có phân trang, tìm kiếm, lọc, sắp xếp)
    getProducts: build.query<GetProductsResponse, GetProductsParams>({
      query: (params: GetProductsParams) => ({
        url: '/products', // Sử dụng endpoint chính cho cả guest và user đã đăng nhập
        method: 'GET',
        params: params,
        authOptional: true, // Gửi token nếu có, không bắt buộc
      }),
      providesTags: ['Products'],
    }),

    getPublicProducts: build.query<GetProductsResponse, GetProductsParams>({
      query: (params: GetProductsParams) => ({
        url: '/products/public',
        method: 'GET',
        params: params,
        // authRequired: false, // Không cần auth cho public
      }),
      providesTags: ['Products'],
    }),

    // 🆕 Tìm kiếm sản phẩm bằng hình ảnh (AI object detection)
    searchProductByImage: build.mutation<any, File>({
      query: (file: File) => {
        const formData = new FormData()
        formData.append('image', file)
        return {
          url: '/products/search-by-image',
          method: 'POST',
          data: formData,
          authRequired: true,
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      },
    }),

    // ✅ Cập nhật sản phẩm
    updateProduct: build.mutation<
      UpdateProductResponse,
      { id: string; data: UpdateProductRequest }
    >({
      query: ({ id, data }) => ({
        url: `/products/${id}`,
        method: 'PUT',
        data: data,
        authRequired: true,
      }),
      invalidatesTags: ['Products'],
    }),

    // ✅ Lấy chi tiết sản phẩm theo ID
    getProductDetail: build.query<GetProductDetailResponse, string>({
      query: (id: string) => ({
        url: `/products/${id}`, // Sử dụng endpoint chính cho cả guest và user đã đăng nhập
        method: 'GET',
        authOptional: true, // Gửi token nếu có, không bắt buộc
      }),
      providesTags: (result, error, id) => [{ type: 'Products', id }],
    }),

    // ✅ Lấy thông tin inventory cho một sản phẩm
    getProductInventory: build.query<GetProductInventoryResponse, GetProductInventoryParams>({
      query: ({ productId, versionId }: GetProductInventoryParams) => ({
        url: `/products/${productId}/inventory${versionId ? `?version_id=${versionId}` : ''}`,
        method: 'GET',
        authRequired: false, // Cho phép guest xem thông tin tồn kho
      }),
      providesTags: (result, error, { productId, versionId }) => [
        { type: 'Products', id: productId },
        { type: 'Inventory', id: `${productId}-${versionId || 'main'}` },
      ],
    }),

    // ✅ Lấy thống kê đánh giá và số lượng đã bán cho sản phẩm
    getProductStats: build.query<GetProductStatsResponse, string>({
      query: (productId: string) => ({
        url: `/products/${productId}/stats`,
        method: 'GET',
        authRequired: false, // Cho phép guest xem thống kê sản phẩm
      }),
      providesTags: (result, error, productId) => [
        { type: 'Products', id: productId },
        { type: 'ProductStats', id: productId },
      ],
    }),

    // ✅ Xóa sản phẩm
    deleteProduct: build.mutation<{ message: string; statusCode: number }, string>({
      query: (id: string) => ({
        url: `/products/${id}`,
        method: 'DELETE',
        authRequired: true,
      }),
      invalidatesTags: ['Products'],
    }),

    // ✅ Tắt (ẩn) sản phẩm
    disableProduct: build.mutation<{ message: string; statusCode: number }, string>({
      query: (id: string) => ({
        url: `/products/${id}/disable`,
        method: 'PUT',
        authRequired: true,
      }),
      invalidatesTags: ['Products'],
    }),

    // ✅ Lấy danh sách status theo nhiều type
    getStatusesByTypes: build.query<StatusItem[], GetStatusesByTypesParams>({
      query: (params: GetStatusesByTypesParams) => {
        // params.types: string[]
        // Truyền lên dạng types=PRODUCT,ORDER
        return {
          url: '/status',
          method: 'GET',
          params: { type: params.type, parent: params.parent || null },
          authRequired: true,
        }
      },
      providesTags: ['Products'],
    }),
    getFavorites: build.query<FavoriteItem[], void>({
      query: () => ({
        url: '/favorites',
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['Favorites'],
    }),
    toggleFavorite: build.mutation<ToggleFavoriteResponse, ToggleFavoriteRequest>({
      query: (data) => ({
        url: '/favorites/toggle',
        method: 'PUT',
        data,
        authRequired: true,
      }),
      invalidatesTags: ['Favorites'],
    }),
  }),
})

export const {
  useCreateProductMutation,
  useGetCategoriesQuery,
  useUploadProductImagesMutation,
  useGetProductsQuery,
  useGetProductDetailQuery,
  useGetProductInventoryQuery,
  useGetProductStatsQuery,
  useUpdateProductMutation,
  useDeleteProductMutation,
  useDisableProductMutation,
  useGetFavoritesQuery,
  useToggleFavoriteMutation,
  useGetStatusesByTypesQuery,
  useSearchProductByImageMutation, // 🆕 export hook mới
  useGetPublicProductsQuery, // ✅ Export hook cho public products
} = productManagementApi
// ✅ Types cho lấy status theo nhóm
export interface GetStatusesByTypeParams {
  type: string // ví dụ: 'PRODUCT'
}

// Unified ProductItem interface matching backend API response
export interface ProductItem {
  id: string
  user_id?: string
  name: string
  slug?: string
  description?: string
  category_id: string
  price: number
  unit: string
  stock: number
  status:
    | string
    | {
        id: string
        label: string
        color?: string
        [key: string]: any
      }
  status_label?: string
  status_label_color?: string
  is_public?: boolean
  seo_keywords?: string | null
  thumbnail_url?: string | null
  source?: string | null
  created_at?: string
  updated_at?: string
  product_images?: ProductImage[]
  product_attribute?: ProductAttribute[]
  product_versions?: ProductVersion[]
  [key: string]: any
}
// ✅ Types cho lấy status theo nhiều type
export interface GetStatusesByTypesParams {
  type: string
  parent?: string | null // ví dụ: ['PRODUCT','ORDER']
}

export interface StatusItem {
  id: string
  type: string
  label: string
  code: string
  description?: string
  color?: string
  icon?: string
  is_final?: boolean
  order_index?: number
  created_at: string
  updated_at: string
}

// ✅ Types cho Categories API
export interface GetCategoriesParams {
  type: 'tree' | 'flat'
  parent_id?: string | null
}

export interface CategoryItem {
  id: string
  name: string
  slug: string
  parentId?: string
  level: number | null
  order_index: number | null
  icon_url: string | null
  is_active: boolean
  created_at: string
  children?: CategoryItem[]
}

export interface GetCategoriesResponse {
  data: CategoryItem[]
  message: string
  statusCode: number
}

// ✅ Types cho Product - Update theo structure mới
export interface ProductImage {
  image_url: string
  public_image_id: string
  is_thumbnail: boolean // ✅ Required field
}

export interface ProductAttribute {
  attribute_name: string
  value: string
  unit: string
  is_searchable: boolean
}

// ✅ Update ProductVersion theo structure mới
export interface ProductVersion {
  name: string
  price: number
  stock: number
  options: { [key: string]: any } // ✅ Object thay vì ProductVersionOptions
  sku_code: string
}

// ✅ Update ProductVersion for update (with id)
export interface ProductVersionUpdate {
  id: string
  name: string
  price: number
  stock: number
  options: { [key: string]: any }
  sku_code: string
}

// ✅ Update CreateProductRequest theo structure mới
export interface CreateProductRequest {
  name: string
  description: string // ✅ Required field
  price: number
  unit: string
  category_id: string
  product_images: ProductImage[] // ✅ Required array
  product_attribute: ProductAttribute[] // ✅ Required array
  product_versions: ProductVersion[] // ✅ Required array
}

// ✅ UpdateProductRequest theo schema mới
export interface UpdateProductRequest {
  name: string
  description: string
  price: number
  unit: string
  category_id: string
  product_images: ProductImage[]
  product_attribute: ProductAttribute[]
  product_versions: ProductVersionUpdate[]
  stock: number
  status: string
  seo_keywords: string
  thumbnail_url: string
  source: string
  is_public: boolean
}

// ✅ UpdateProductResponse (reuse CreateProductResponse)
export type UpdateProductResponse = CreateProductResponse

export interface CreateProductResponse {
  data: ProductItem
  message: string
  statusCode: number
}

// ✅ Types cho Upload Images API
export interface UploadedImage {
  id?: string
  url?: string
  public_image_id?: string
  image_url?: string
  src?: string
  public_id?: string
  imageId?: string
}

export interface UploadImagesResponse {
  data: UploadedImage[]
  message: string
  statusCode: number
}

// ✅ Types cho GetProducts API
export interface GetProductsParams {
  page?: number
  size?: number
  search?: string
  category_id?: string
  user_id?: string
  status?: string
  source?: string
  price_min?: number
  price_max?: number
  created_from?: string
  created_to?: string
  has_stock?: boolean
  order_by?: string
  order_dir?: string
  sort_by?: string
  sort_order?: string
}

export interface GetProductsResponse {
  data: ProductItem[]
  total: number
  page: number
  size: number
  message: string
  statusCode: number
}

// ✅ Types cho GetStatusList API
export interface GetStatusListParams {
  type: 'PRODUCT' | 'ORDER' | 'PAYMENT' | 'IMAGE'
}

export interface StatusItem {
  id: string
  name: string
  code: string
  type: string
  label: string // Đảm bảo chỉ có một label kiểu string
  description?: string
  is_active: boolean
  created_at: string
}

export interface GetStatusListResponse {
  data: StatusItem[]
  message: string
  statusCode: number
}
export interface FavoriteItem {
  id: string
  user_id: string
  name: string
  slug: string
  description: string
  category_id: string
  price: string
  unit: string
  stock: number
  status_id: string
  is_public: boolean
  seo_keywords: string | null
  thumbnail_url: string | null
  source: string | null
  created_at: string
  updated_at: string
}

export interface FavoriteListResponse {
  data: FavoriteItem[]
  message: string
  statusCode: number
}

export interface ToggleFavoriteRequest {
  product_id: string
}

export interface ToggleFavoriteResponse {
  liked: boolean
  message: string
  statusCode: number
}
// ✅ Export aliases
export type { CategoryItem as ProductCategory, ProductItem as Product }

export interface GetProductDetailResponse extends ProductItem {
  // Trả về ProductItem trực tiếp, không wrap trong data
}

export interface GetProductInventoryParams {
  productId: string
  versionId?: string
}

export interface GetProductInventoryResponse {
  product_id: string
  version_id?: string
  available: number
  stock_quantity: number
  reserved_quantity: number
}

export interface GetProductStatsResponse {
  total_reviews: number
  average_rating: number
  total_sold: number
  reviews: Array<{
    rating: number
    content?: string
    created_at: string
    user: {
      first_name?: string
      last_name?: string
      email?: string
      avatar_url?: string
    }
  }>
}
