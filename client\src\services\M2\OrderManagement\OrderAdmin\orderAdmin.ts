import { createApi } from '@reduxjs/toolkit/query/react'
import { axiosBaseQuery } from '../../../../libs/axios/axiosBase'

const baseUrl = 'http://localhost:8080'

// Types cho request/response
export interface OrderManagementListParams {
  page?: number;
  size?: number;
  search?: string;
  status_id?: string;
}

export interface OrderManagementListResponse {
  data?: any;
  message?: string;
  statusCode?: number;
}

export interface OrderDetailResponse {
  data?: any;
  message?: string;
  statusCode?: number;
}

export interface UpdateOrderStatusRequest {
  status_id: string;
}

export interface CancelRequestListParams {
  page?: number;
  size?: number;
  search?: string;
  status_id?: string;
}

export interface CancelRequestListResponse {
  data?: any;
  message?: string;
  statusCode?: number;
}

export const orderAdminApi = createApi({
  reducerPath: 'orderAdminApi',
  baseQuery: axiosBaseQuery({ baseUrl }),
  tagTypes: ['OrdersAdmin'],

  endpoints: (build) => ({
    // ✅ Lấy danh sách tất cả đơn hàng (admin)
    getAdminOrders: build.query<OrderManagementListResponse, OrderManagementListParams>({
      query: (params) => ({
        url: '/orders-management/admin/order', // Sử dụng endpoint hiện tại
        method: 'GET',
        params: params,
        authRequired: true,
      }),
      providesTags: ['OrdersAdmin'],
    }),

    // ✅ Lấy chi tiết đơn hàng (admin)
    getAdminOrderDetail: build.query<OrderDetailResponse, string>({
      query: (id: string) => ({
        url: `/orders-management/admin/order/${id}`, // Sử dụng endpoint hiện tại
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['OrdersAdmin'],
    }),

    // ✅ Cập nhật trạng thái đơn hàng (admin)
    updateAdminOrderStatus: build.mutation<OrderDetailResponse, { id: string; data: UpdateOrderStatusRequest }>({
      query: ({ id, data }) => ({
        url: `/orders-management/${id}/status`, // Sử dụng endpoint hiện tại
        method: 'POST',
        data: data,
        authRequired: true,
      }),
      invalidatesTags: ['OrdersAdmin'],
    }),

    // ✅ Lấy danh sách yêu cầu hủy đơn hàng (admin)
    getAdminCancelOrderRequests: build.query<CancelRequestListResponse, CancelRequestListParams>({
      query: (params) => ({
        url: '/orders-management/admin/cancel-requests', // Sử dụng endpoint hiện tại
        method: 'GET',
        params: params,
        authRequired: true,
      }),
      providesTags: ['OrdersAdmin'],
    }),

    // ✅ Duyệt yêu cầu hủy đơn hàng (admin)
    acceptAdminCancelRequest: build.mutation<OrderDetailResponse, string>({
      query: (id: string) => ({
        url: `/orders-management/${id}/accept-cancel-request`, // Sử dụng endpoint hiện tại
        method: 'POST',
        authRequired: true,
      }),
      invalidatesTags: ['OrdersAdmin'],
    }),
  }),
})

export const {
  useGetAdminOrdersQuery,
  useGetAdminOrderDetailQuery,
  useUpdateAdminOrderStatusMutation,
  useGetAdminCancelOrderRequestsQuery,
  useAcceptAdminCancelRequestMutation,
} = orderAdminApi
