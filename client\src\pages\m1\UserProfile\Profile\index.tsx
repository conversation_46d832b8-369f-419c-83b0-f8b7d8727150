import React, { useEffect, useState, useCallback, useMemo } from 'react'
import {
  Avatar,
  Typography,
  Card,
  Form,
  Button,
  Tabs,
  Table,
  Tree,
  Statistic,
  Row,
  Col,
  Select,
  Space,
  Switch,
  Input,
  Alert,
  Modal,
  Spin,
  Divider,
  Tag,
  List,
} from 'antd'
import {
  UserOutlined,
  SafetyOutlined,
  MailOutlined,
  MobileOutlined,
  ExclamationCircleOutlined,
  LockOutlined,
  DesktopOutlined,
  TeamOutlined,
  TrophyOutlined,
  GiftOutlined,
  StarOutlined,
  CalendarOutlined,
  EnvironmentOutlined,
  IdcardOutlined,
  PhoneOutlined,
  GlobalOutlined,
  QrcodeOutlined,
  EditOutlined,
  EyeOutlined,
  KeyOutlined,
  SecurityScanOutlined,
  LogoutOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons'
import styles from './Profile.module.scss'
import ProfileUpdateForm from './ProfileUpdateForm'

import { useGetUserProfileHook, useUpdateAvatarHook } from '@/hooks/user'
import {
  useGetAffiliateMeHook,
  useGetAffiliateTreeHook,
  useGetAffiliateStatsHook,
  useGetAffiliateDirectHook,
  useGetAffiliateIndirectHook,
} from '@/hooks/affiliate/useAffiliate'
import {
  useRequest2FAOTP,
  useVerify2FAOTP,
  useDisable2FA,
  useChangePassword,
} from '@/hooks/auth/useLogin'
import { useToast } from '@/components/Toast'
import { getAvatarUrl } from '@/utils/avatar'
import UserQr from './Qr'
import UploadAvatar from './UploadAvatar'

import { motion } from 'framer-motion'

const { Text, Title, Paragraph } = Typography
const { TabPane } = Tabs
const { Option } = Select

// Interfaces
interface UserData {
  id: string
  email: string
  gender: string
  fullName: string
  dateOfBirth: string
  citizenId: string
  phone: string
  address: string
  points: number
  qrCode: string
}

interface ChangePasswordModalProps {
  open: boolean
  onCancel: () => void
  onSuccess: () => void
  isProcessing: boolean
  styles: any
}

interface SessionsModalProps {
  open: boolean
  onCancel: () => void
  styles: any
}

interface SecurityTabProps {
  is2FAEnabled: boolean
  showOTPSetup: boolean
  showDisableModal: boolean
  showChangePasswordModal: boolean
  showSessionsModal: boolean
  otpEmail: string
  otpCode: string
  disableOtpCode: string
  disableOtpSent: boolean
  otpSent: boolean
  isProcessing: boolean
  userData: UserData
  styles: any
  handle2FAToggle: (checked: boolean) => void
  handleSendOTP: () => void
  handleVerifyOTP: () => void
  handleCancelOTPSetup: () => void
  handleSendDisableOTP: () => void
  handleDisable2FA: () => void
  handleCancelDisable: () => void
  handleShowChangePassword: () => void
  handleCancelChangePassword: () => void
  handleShowSessions: () => void
  handleCancelSessions: () => void
  handleOtpEmailChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleOtpCodeChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleDisableOtpCodeChange: (e: React.ChangeEvent<HTMLInputElement>) => void
}

// Change Password Modal Component
const ChangePasswordModal: React.FC<ChangePasswordModalProps> = ({
  open,
  onCancel,
  onSuccess,
  isProcessing,
  styles,
}) => {
  const [form] = Form.useForm()
  const changePassword = useChangePassword() as any

    const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      const success = await changePassword(values as any)
      if (success) {
        form.resetFields()
        onSuccess()
      }
    } catch (err) {
      // Handle validation errors
      }
    }

    const handleCancel = () => {
    form.resetFields()
      onCancel()
    }

    return (
      <Modal
      title="Đổi mật khẩu"
        open={open}
        onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          Hủy
        </Button>,
          <Button
          key="submit"
            type="primary"
            loading={isProcessing}
          onClick={handleSubmit}
          >
            Đổi mật khẩu
          </Button>,
        ]}
      >
      <Form form={form} layout="vertical">
        <Form.Item
          name="currentPassword"
          label="Mật khẩu hiện tại"
          rules={[{ required: true, message: 'Vui lòng nhập mật khẩu hiện tại' }]}
        >
          <Input.Password />
        </Form.Item>
        <Form.Item
          name="newPassword"
          label="Mật khẩu mới"
          rules={[
            { required: true, message: 'Vui lòng nhập mật khẩu mới' },
            { min: 6, message: 'Mật khẩu phải có ít nhất 6 ký tự' },
          ]}
        >
          <Input.Password />
        </Form.Item>
        <Form.Item
          name="confirmPassword"
          label="Xác nhận mật khẩu mới"
          dependencies={['newPassword']}
          rules={[
            { required: true, message: 'Vui lòng xác nhận mật khẩu mới' },
            ({ getFieldValue }) => ({
              validator(_: any, value: any) {
                if (!value || getFieldValue('newPassword') === value) {
                  return Promise.resolve()
                }
                return Promise.reject(new Error('Mật khẩu xác nhận không khớp'))
              },
            }),
          ]}
        >
          <Input.Password />
        </Form.Item>
      </Form>
      </Modal>
    )
  }

// Sessions Modal Component
const SessionsModal: React.FC<SessionsModalProps> = ({ open, onCancel, styles }) => {
  const sessions: any[] = []
  const isLoading = false

  const parseUserAgent = (userAgent: string) => {
    if (!userAgent) return { browser: 'Unknown', os: 'Unknown' }
    
    const browserMatch = userAgent.match(/(chrome|firefox|safari|edge|opera)\/?\s*(\d+)/i)
    const osMatch = userAgent.match(/(windows|mac|linux|android|ios)\s*[^)]*/i)
    
    return {
      browser: browserMatch ? `${browserMatch[1]} ${browserMatch[2]}` : 'Unknown',
      os: osMatch ? osMatch[1] : 'Unknown',
    }
  }

  const formatIPAddress = (ipAddress: string) => {
    return ipAddress || 'N/A'
  }

  const getDeviceIcon = (userAgent: string) => {
    if (!userAgent) return <DesktopOutlined />
    
    if (userAgent.includes('Mobile')) return <MobileOutlined />
    if (userAgent.includes('Tablet')) return <DesktopOutlined />
    return <DesktopOutlined />
  }

  const sessionColumns = [
    {
      title: 'Thiết bị',
      key: 'device',
      render: (_: any, record: any) => (
        <Space>
          {getDeviceIcon(record.userAgent)}
          <div>
            <div>{parseUserAgent(record.userAgent).browser}</div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {parseUserAgent(record.userAgent).os}
            </Text>
            </div>
        </Space>
      ),
    },
    {
      title: 'IP Address',
      dataIndex: 'ipAddress',
      key: 'ipAddress',
      render: (ip: string) => formatIPAddress(ip),
    },
    {
      title: 'Đăng nhập lúc',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleString('vi-VN'),
    },
    {
      title: 'Trạng thái',
      key: 'status',
      render: (_: any, record: any) => (
        <Tag color={record.isActive ? 'green' : 'default'}>
          {record.isActive ? 'Hoạt động' : 'Không hoạt động'}
        </Tag>
      ),
    },
  ]

  return (
    <Modal
      title="Phiên đăng nhập"
      open={open}
      onCancel={onCancel}
      width={800}
      footer={[
        <Button key="close" onClick={onCancel}>
          Đóng
        </Button>,
      ]}
    >
      {isLoading ? (
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <Spin size="large" />
      </div>
      ) : (
      <Table
          columns={sessionColumns}
          dataSource={Array.isArray(sessions) ? sessions : []}
        rowKey="id"
          pagination={false}
        size="small"
      />
      )}
    </Modal>
  )
}

// Security Tab Component
const SecurityTab: React.FC<SecurityTabProps> = ({
    is2FAEnabled,
    showOTPSetup,
    showDisableModal,
    showChangePasswordModal,
    showSessionsModal,
    otpEmail,
    otpCode,
    disableOtpCode,
    disableOtpSent,
    otpSent,
    isProcessing,
    userData,
    styles,
    handle2FAToggle,
    handleSendOTP,
    handleVerifyOTP,
    handleCancelOTPSetup,
    handleSendDisableOTP,
    handleDisable2FA,
    handleCancelDisable,
    handleShowChangePassword,
    handleCancelChangePassword,
    handleShowSessions,
    handleCancelSessions,
    handleOtpEmailChange,
    handleOtpCodeChange,
    handleDisableOtpCodeChange,
}) => {
  return (
    <div className={styles.securityTab}>
      <div className={styles.securityOverview}>
        <Title level={4}>Tổng quan bảo mật</Title>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={8}>
      <Card className={styles.securityCard}>
        <div className={styles.securityItem}>
                <div className={styles.securityIcon}>
                  <MailOutlined />
                </div>
                <div className={styles.securityInfo}>
                  <Text strong>Email</Text>
                  <Text type="secondary">{userData.email}</Text>
                  <Tag color="green" icon={<CheckCircleOutlined />}>
                    Đã xác thực
                  </Tag>
                </div>
              </div>
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={8}>
            <Card className={styles.securityCard}>
              <div className={styles.securityItem}>
                <div className={styles.securityIcon}>
                  <PhoneOutlined />
                </div>
                <div className={styles.securityInfo}>
                  <Text strong>Số điện thoại</Text>
                  <Text type="secondary">
                    {userData.phone || 'Chưa cập nhật'}
                  </Text>
                  <Tag color={userData.phone ? "green" : "orange"} icon={userData.phone ? <CheckCircleOutlined /> : <InfoCircleOutlined />}>
                    {userData.phone ? 'Đã xác thực' : 'Chưa cập nhật'}
                  </Tag>
                </div>
              </div>
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={8}>
            <Card className={styles.securityCard}>
              <div className={styles.securityItem}>
                <div className={styles.securityIcon}>
                  <SecurityScanOutlined />
                </div>
                <div className={styles.securityInfo}>
                  <Text strong>Xác thực 2 yếu tố</Text>
                  <Text type="secondary">
                    {is2FAEnabled ? 'Đã bật' : 'Chưa bật'}
                  </Text>
                  <Tag color={is2FAEnabled ? 'green' : 'orange'} icon={is2FAEnabled ? <CheckCircleOutlined /> : <InfoCircleOutlined />}>
                    {is2FAEnabled ? 'Bảo mật cao' : 'Cần bật'}
                  </Tag>
                </div>
              </div>
            </Card>
          </Col>
        </Row>
      </div>

      <Divider />

      <div className={styles.securityActions}>
        <Title level={4}>Hành động bảo mật</Title>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12}>
            <Card className={styles.actionCard}>
              <div className={styles.actionHeader}>
                <KeyOutlined className={styles.actionIcon} />
                <div>
                  <Title level={5}>Đổi mật khẩu</Title>
                  <Text type="secondary">Cập nhật mật khẩu định kỳ để bảo mật tài khoản</Text>
                </div>
              </div>
              <Button type="primary" onClick={handleShowChangePassword} className={styles.actionButton}>
                Đổi mật khẩu
              </Button>
            </Card>
          </Col>
          <Col xs={24} sm={12}>
            <Card className={styles.actionCard}>
              <div className={styles.actionHeader}>
                <SecurityScanOutlined className={styles.actionIcon} />
                <div>
                  <Title level={5}>Xác thực 2 yếu tố</Title>
                  <Text type="secondary">Bật 2FA để tăng cường bảo mật tài khoản</Text>
                </div>
            </div>
            <Switch
              checked={is2FAEnabled}
              onChange={handle2FAToggle}
                className={styles.actionSwitch}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12}>
            <Card className={styles.actionCard}>
              <div className={styles.actionHeader}>
                <EyeOutlined className={styles.actionIcon} />
                <div>
                  <Title level={5}>Phiên đăng nhập</Title>
                  <Text type="secondary">Xem các thiết bị đang đăng nhập</Text>
                </div>
              </div>
              <Button onClick={handleShowSessions} className={styles.actionButton}>
                Xem phiên
              </Button>
            </Card>
          </Col>
        </Row>
          </div>

      {/* Modals */}
      <ChangePasswordModal
        open={showChangePasswordModal}
        onCancel={handleCancelChangePassword}
        onSuccess={handleCancelChangePassword}
        isProcessing={isProcessing}
        styles={styles}
      />
      <SessionsModal open={showSessionsModal} onCancel={handleCancelSessions} styles={styles} />

      {/* 2FA Setup Modal */}
      <Modal
        title="Thiết lập xác thực 2 yếu tố"
        open={showOTPSetup}
        onCancel={handleCancelOTPSetup}
        footer={null}
      >
        <div className={styles.otpSetup}>
              <Alert
            message="Bảo mật tài khoản"
            description="Xác thực 2 yếu tố giúp bảo vệ tài khoản của bạn khỏi truy cập trái phép."
            type="info"
                showIcon
            style={{ marginBottom: 16 }}
          />
          <Form layout="vertical">
            <Form.Item label="Email nhận mã OTP">
              <Input
                value={otpEmail}
                onChange={handleOtpEmailChange}
                placeholder="Nhập email"
              />
            </Form.Item>
            {otpSent && (
              <Form.Item label="Mã OTP">
                <Input
                  value={otpCode}
                  onChange={handleOtpCodeChange}
                  placeholder="Nhập mã OTP"
                />
              </Form.Item>
            )}
            <Space>
              {!otpSent ? (
                  <Button
                    type="primary"
                  onClick={handleSendOTP}
                    loading={isProcessing}
                  >
                  Gửi mã OTP
                  </Button>
              ) : (
                  <Button
                  type="primary"
                  onClick={handleVerifyOTP}
                  loading={isProcessing}
                >
                  Xác thực
                  </Button>
              )}
              <Button onClick={handleCancelOTPSetup}>Hủy</Button>
                </Space>
          </Form>
              </div>
      </Modal>

      {/* Disable 2FA Modal */}
      <Modal
        title="Tắt xác thực 2 yếu tố"
        open={showDisableModal}
        onCancel={handleCancelDisable}
        footer={null}
      >
        <div className={styles.otpSetup}>
        <Alert
          message="Cảnh báo bảo mật"
            description="Tắt xác thực 2 yếu tố sẽ làm giảm mức độ bảo mật của tài khoản."
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
          <Form layout="vertical">
            {!disableOtpSent ? (
              <Button
                type="primary"
                danger
                onClick={handleSendDisableOTP}
                loading={isProcessing}
              >
                Gửi mã OTP xác nhận
            </Button>
            ) : (
              <>
                <Form.Item label="Mã OTP xác nhận">
              <Input
                value={disableOtpCode}
                onChange={handleDisableOtpCodeChange}
                    placeholder="Nhập mã OTP"
                  />
                </Form.Item>
                <Space>
            <Button
              type="primary"
              danger
              onClick={handleDisable2FA}
              loading={isProcessing}
            >
              Tắt 2FA
            </Button>
                  <Button onClick={handleCancelDisable}>Hủy</Button>
                </Space>
              </>
          )}
          </Form>
        </div>
      </Modal>
    </div>
  )
}

const ProfilePage = () => {
  const [isEditing, setIsEditing] = useState(false)
  const [selectedLevel, setSelectedLevel] = useState(2)
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null)
  const [form] = Form.useForm()
  const { data, refetch } = useGetUserProfileHook()
  const [showOtpEmail, setShowOtpEmail] = useState(false)
  const uploadAvt = useUpdateAvatarHook()

  // States cho 2FA
  const [is2FAEnabled, setIs2FAEnabled] = useState(false)
  const [showOTPSetup, setShowOTPSetup] = useState(false)
  const [showDisableModal, setShowDisableModal] = useState(false)
  const [otpEmail, setOtpEmail] = useState('')
  const [otpCode, setOtpCode] = useState('')
  const [disableOtpCode, setDisableOtpCode] = useState('')
  const [disableOtpSent, setDisableOtpSent] = useState(false)
  const [otpSent, setOtpSent] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)

  // States cho Change Password và Sessions
  const [showChangePasswordModal, setShowChangePasswordModal] = useState(false)
  const [showSessionsModal, setShowSessionsModal] = useState(false)

  // Hooks 2FA, Change Password và Sessions
  const requestOTP = useRequest2FAOTP()
  const verifyOTP = useVerify2FAOTP()
  const { sendOTP: sendDisableOTP, disable2FA } = useDisable2FA()

  // Hooks affiliate
  const { data: affiliateMe } = useGetAffiliateMeHook()
  const { data: affiliateTree } = useGetAffiliateTreeHook()
  const { data: affiliateStats } = useGetAffiliateStatsHook()
  const { data: affiliateDirect } = useGetAffiliateDirectHook()
  const { data: affiliateIndirect } = useGetAffiliateIndirectHook(selectedLevel)

  const profileData = useMemo(
    () => ({
      avatarUrl: avatarPreview || getAvatarUrl(data?.avatarUrl ? `${data.avatarUrl}?t=${Date.now()}` : data?.avatarUrl),
      fullName: data?.fullName ?? '',
      uIdCode: data?.uIdCode ?? '',
      role: data?.role ?? '',
    }),
    [avatarPreview, data]
  )

  const userData = useMemo(
    (): UserData => ({
      id: data?.id ?? '',
      email: data?.email ?? '',
      gender: data?.gender ?? '',
      fullName: data?.fullName ?? '',
      dateOfBirth: data?.dateOfBirth ?? '',
      citizenId: data?.citizenId ?? '',
      phone: data?.phoneNumber || '',
      address: data?.address ?? '',
      points: 0,
      qrCode: data?.uIdCode ?? '',
    }),
    [data]
  )

  useEffect(() => {
    if (data) {
      form.setFieldsValue({
        email: data.email,
        gender: data.gender,
        dateOfBirth: data.dateOfBirth,
        phone: data.phoneNumber || '',
        address: data.address ?? '',
        street: data.address ?? '',
      })

      setIs2FAEnabled(data?.isEnable2FA || false)
    }
  }, [data, form])

  const handleAvatarUpload = async (file: File, previewUrl: string) => {
    try {
      setAvatarPreview(previewUrl)
      const res = await uploadAvt(file)

      if (res) {
        setTimeout(() => {
          setAvatarPreview(null)
          refetch()
        }, 1000)
      } else {
        setAvatarPreview(null)
      }
    } catch (error) {
      setAvatarPreview(null)
    }
  }

  const handle2FAToggle = useCallback(
    async (checked: boolean) => {
      if (checked) {
        setShowOTPSetup(true)
        setOtpEmail(userData.email)
      } else {
        setShowDisableModal(true)
      }
    },
    [userData.email]
  )

  const handleSendOTP = useCallback(async () => {
    if (!otpEmail) return

    setIsProcessing(true)
    const success = await requestOTP(otpEmail)

    if (success) {
      setOtpSent(true)
    }
    setIsProcessing(false)
  }, [otpEmail, requestOTP])

  const handleVerifyOTP = useCallback(async () => {
    if (!otpCode) return

    setIsProcessing(true)
    const success = await verifyOTP(otpEmail, otpCode)

    if (success) {
      setIs2FAEnabled(true)
      setShowOTPSetup(false)
      setOtpSent(false)
      setOtpCode('')
    }
    setIsProcessing(false)
  }, [otpEmail, otpCode, verifyOTP])

  const handleCancelOTPSetup = useCallback(() => {
    setShowOTPSetup(false)
    setOtpSent(false)
    setOtpCode('')
  }, [])

  const handleSendDisableOTP = useCallback(async () => {
    setIsProcessing(true)
    const success = await sendDisableOTP(userData.email)

    if (success) {
      setDisableOtpSent(true)
    }
    setIsProcessing(false)
  }, [userData.email, sendDisableOTP])

  const handleDisable2FA = useCallback(async () => {
    if (!disableOtpCode) return

    setIsProcessing(true)
    const success = await disable2FA(userData.email, disableOtpCode)

    if (success) {
      setIs2FAEnabled(false)
      setShowDisableModal(false)
      setDisableOtpSent(false)
      setDisableOtpCode('')
    }
    setIsProcessing(false)
  }, [userData.email, disableOtpCode, disable2FA])

  const handleCancelDisable = useCallback(() => {
    setShowDisableModal(false)
    setDisableOtpSent(false)
    setDisableOtpCode('')
  }, [])

  const handleShowChangePassword = useCallback(() => {
    setShowChangePasswordModal(true)
  }, [])

  const handleCancelChangePassword = useCallback(() => {
    setShowChangePasswordModal(false)
  }, [])

  const handleShowSessions = useCallback(async () => {
    setShowSessionsModal(true)
  }, [])

  const handleCancelSessions = useCallback(() => {
    setShowSessionsModal(false)
  }, [])

  const handleOtpEmailChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setOtpEmail(e.target.value)
  }, [])

  const handleOtpCodeChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setOtpCode(e.target.value)
  }, [])

  const handleDisableOtpCodeChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setDisableOtpCode(e.target.value)
  }, [])

  const transformTreeData = useCallback((nodes: any[], level = 1): any[] => {
    if (!nodes || !Array.isArray(nodes)) return []

    return nodes.map((item) => ({
      key: `${item.id}-${level}`,
      title: `${item.name || item.user?.fullName || item.user?.email || 'N/A'} (F${level})`,
      children:
        item.children && item.children.length > 0
          ? transformTreeData(item.children, level + 1)
          : [],
    }))
  }, [])

  const treeData = useMemo(
    () => transformTreeData(affiliateTree?.tree || []),
    [affiliateTree, transformTreeData]
  )

      return (
    <div className={styles.profilePage}>
      {/* Hero Section */}
      <section className={styles.heroSection}>
        <div className={styles.container}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className={styles.heroContent}
          >
            <div className={styles.heroLeft}>
              <div className={styles.avatarSection}>
                  <Avatar
                    size={120}
                    src={profileData.avatarUrl}
                    icon={<UserOutlined />}
                  className={styles.heroAvatar}
                  />
                  <UploadAvatar onUploadSuccess={handleAvatarUpload}>
                    <Button className={styles.changeAvatarBtn} size="small" type="default">
                    <EditOutlined />
                      Đổi ảnh
                    </Button>
                  </UploadAvatar>
                </div>
              <div className={styles.heroInfo}>
                <Title level={2} className={styles.heroName}>
                    {profileData.fullName}
                </Title>
                <Text className={styles.heroRole}>{profileData.role}</Text>
                <div className={styles.heroStats}>
                  <div className={styles.statItem}>
                    <TrophyOutlined />
                    <span>Điểm: {userData.points}</span>
                    </div>
                  <div className={styles.statItem}>
                    <TeamOutlined />
                    <span>F1: {affiliateStats?.totalF1 || 0}</span>
                    </div>
                    </div>
                  </div>
                </div>
            <div className={styles.heroRight}>
                <div className={styles.qrSection}>
                  <UserQr uIdCode={profileData.uIdCode} />
                </div>
              </div>
          </motion.div>
            </div>
      </section>

          {/* Main Content */}
      <section className={styles.mainSection}>
        <div className={styles.container}>
              <Tabs defaultActiveKey="1" className={styles.profileTabs}>
                <TabPane tab="Thông tin cá nhân" key="1">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <div className={styles.personalInfoSection}>
                  <Title level={3}>Thông tin cá nhân</Title>
                  <Row gutter={[24, 24]}>
                    <Col xs={24} lg={16}>
                  <ProfileUpdateForm
                    form={form}
                    isEditing={isEditing}
                    setIsEditing={setIsEditing}
                    userData={userData}
                    showOtpEmail={showOtpEmail}
                    setShowOtpEmail={setShowOtpEmail}
                  />
                    </Col>
                    <Col xs={24} lg={8}>
                      <div className={styles.infoCard}>
                        <Title level={4}>Thông tin cơ bản</Title>
                        <div className={styles.infoList}>
                          <div className={styles.infoItem}>
                            <IdcardOutlined />
                            <div>
                              <Text strong>Mã định danh</Text>
                              <Text copyable>{profileData.uIdCode}</Text>
                            </div>
                          </div>
                          <div className={styles.infoItem}>
                            <MailOutlined />
                            <div>
                              <Text strong>Email</Text>
                              <Text>{userData.email}</Text>
                            </div>
                          </div>
                          <div className={styles.infoItem}>
                            <PhoneOutlined />
                            <div>
                              <Text strong>Số điện thoại</Text>
                              <Text>{userData.phone}</Text>
                            </div>
                          </div>
                          <div className={styles.infoItem}>
                            <CalendarOutlined />
                            <div>
                              <Text strong>Ngày sinh</Text>
                              <Text>{userData.dateOfBirth || 'Chưa cập nhật'}</Text>
                            </div>
                          </div>
                          <div className={styles.infoItem}>
                            <EnvironmentOutlined />
                            <div>
                              <Text strong>Địa chỉ</Text>
                              <Text>{userData.address || 'Chưa cập nhật'}</Text>
                            </div>
                          </div>
                        </div>
                      </div>
                    </Col>
                  </Row>
                </div>
              </motion.div>
                </TabPane>

                <TabPane tab="Affiliate" key="2">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <div className={styles.affiliateSection}>
                  <Title level={3}>Chương trình Affiliate</Title>
                  
                  {/* Stats Cards */}
                    <Row gutter={[16, 16]} className={styles.statsRow}>
                      <Col xs={24} sm={8}>
                      <Card className={styles.statCard}>
                        <Statistic
                          title="Tổng F1"
                          value={affiliateStats?.totalF1 || 0}
                          prefix={<TeamOutlined />}
                        />
                      </Card>
                      </Col>
                      <Col xs={24} sm={8}>
                      <Card className={styles.statCard}>
                        <Statistic
                          title="Tổng hệ thống"
                          value={affiliateStats?.totalSystem || 0}
                          prefix={<GlobalOutlined />}
                        />
                      </Card>
                      </Col>
                      <Col xs={24} sm={8}>
                      <Card className={styles.statCard}>
                        <Statistic
                          title="Hoa hồng tiềm năng"
                          value={affiliateStats?.potentialCommission || 0}
                          suffix="VND"
                          prefix={<GiftOutlined />}
                        />
                      </Card>
                      </Col>
                    </Row>

                  {/* Referral Link */}
                  {affiliateMe && (
                    <Card className={styles.referralCard}>
                      <Title level={4}>Link giới thiệu</Title>
                      <div className={styles.referralLink}>
                        <Text copyable ellipsis className={styles.linkText}>
                          {affiliateMe.referralLink}
                        </Text>
                      </div>
                    </Card>
                  )}

                  {/* Tree View */}
                  <Card className={styles.treeCard}>
                    <Title level={4}>Cây hệ thống</Title>
                    <div className={styles.treeContainer}>
                      {treeData.length > 0 ? (
                        <Tree treeData={treeData} defaultExpandAll />
                      ) : (
                        <div className={styles.emptyState}>
                          <TeamOutlined className={styles.emptyIcon} />
                          <Text>Chưa có dữ liệu cây hệ thống</Text>
                        </div>
                      )}
                    </div>
                  </Card>

                  {/* Direct F1 List */}
                  <Card className={styles.listCard}>
                    <Title level={4}>Danh sách F1 trực tiếp</Title>
                    <div className={styles.affiliateList}>
                      {affiliateDirect && affiliateDirect.length > 0 ? (
                        <List
                          dataSource={affiliateDirect}
                          renderItem={(item: any) => (
                            <List.Item className={styles.affiliateItem}>
                              <List.Item.Meta
                                avatar={<Avatar icon={<UserOutlined />} />}
                                title={item.user?.fullName || item.user?.email || 'N/A'}
                                description={
                                  <div className={styles.affiliateMeta}>
                                    <Text type="secondary">{item.user?.uidCode}</Text>
                                    <Text type="secondary">{item.user?.phone}</Text>
                                    <Text type="secondary">
                                      {new Date(item.user?.createdAt).toLocaleDateString('vi-VN')}
                                    </Text>
                                  </div>
                                }
                              />
                            </List.Item>
                          )}
                        />
                      ) : (
                        <div className={styles.emptyState}>
                          <TeamOutlined className={styles.emptyIcon} />
                          <Text>Chưa có F1 trực tiếp</Text>
                    </div>
                      )}
                    </div>
                  </Card>

                  {/* Indirect List */}
                  <Card className={styles.listCard}>
                    <div className={styles.listHeader}>
                      <Title level={4}>Danh sách gián tiếp</Title>
                      <Select
                        value={selectedLevel}
                        onChange={setSelectedLevel}
                        style={{ width: 120 }}
                      >
                        <Option value={2}>F2</Option>
                        <Option value={3}>F3</Option>
                        <Option value={4}>F4</Option>
                        <Option value={5}>F5</Option>
                      </Select>
                    </div>
                    <div className={styles.affiliateList}>
                      {affiliateIndirect && affiliateIndirect.length > 0 ? (
                        <List
                          dataSource={affiliateIndirect}
                          renderItem={(item: any) => (
                            <List.Item className={styles.affiliateItem}>
                              <List.Item.Meta
                                avatar={<Avatar icon={<UserOutlined />} />}
                                title={item.user?.fullName || item.user?.email || 'N/A'}
                                description={
                                  <div className={styles.affiliateMeta}>
                                    <Text type="secondary">{item.user?.uidCode}</Text>
                                    <Text type="secondary">{item.user?.phone}</Text>
                                    <Text type="secondary">
                                      {new Date(item.user?.createdAt).toLocaleDateString('vi-VN')}
                                    </Text>
                                  </div>
                                }
                              />
                              <Tag color="blue">F{selectedLevel}</Tag>
                            </List.Item>
                          )}
                        />
                      ) : (
                        <div className={styles.emptyState}>
                          <TeamOutlined className={styles.emptyIcon} />
                          <Text>Chưa có F{selectedLevel}</Text>
                    </div>
                      )}
                  </div>
                  </Card>
                </div>
              </motion.div>
                </TabPane>

            <TabPane tab="Bảo mật" key="3">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                  <SecurityTab
                    is2FAEnabled={is2FAEnabled}
                    showOTPSetup={showOTPSetup}
                    showDisableModal={showDisableModal}
                    showChangePasswordModal={showChangePasswordModal}
                    showSessionsModal={showSessionsModal}
                    otpEmail={otpEmail}
                    otpCode={otpCode}
                    disableOtpCode={disableOtpCode}
                    disableOtpSent={disableOtpSent}
                    otpSent={otpSent}
                    isProcessing={isProcessing}
                    userData={userData}
                    styles={styles}
                    handle2FAToggle={handle2FAToggle}
                    handleSendOTP={handleSendOTP}
                    handleVerifyOTP={handleVerifyOTP}
                    handleCancelOTPSetup={handleCancelOTPSetup}
                    handleSendDisableOTP={handleSendDisableOTP}
                    handleDisable2FA={handleDisable2FA}
                    handleCancelDisable={handleCancelDisable}
                    handleShowChangePassword={handleShowChangePassword}
                    handleCancelChangePassword={handleCancelChangePassword}
                    handleShowSessions={handleShowSessions}
                    handleCancelSessions={handleCancelSessions}
                    handleOtpEmailChange={handleOtpEmailChange}
                    handleOtpCodeChange={handleOtpCodeChange}
                    handleDisableOtpCodeChange={handleDisableOtpCodeChange}
                  />
              </motion.div>
                </TabPane>
              </Tabs>
            </div>
      </section>
    </div>
  )
}

export default ProfilePage
