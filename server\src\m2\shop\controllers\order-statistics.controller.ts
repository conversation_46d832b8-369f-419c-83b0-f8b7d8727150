import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import {
  OrderStatisticsDto,
  OrderTrendDto,
  PaymentMethodStatsDto,
  RevenueDetailsDto,
  CancelledOrderListDto,
  OrderStatisticsQueryDto,
} from '../dto/order-statistics.dto';
import { OrderStatisticsService } from '../services/order-statistics.service';

@ApiTags('Order Statistics')
@Controller('order-statistics')
@ApiBearerAuth('access-token')
@UseGuards(AuthGuard('jwt'))
export class OrderStatisticsController {
  constructor(private readonly service: OrderStatisticsService) {}

  @Get()
  @ApiOperation({ summary: 'Lấy thống kê tổng quan đơn hàng' })
  @ApiResponse({ status: 200, type: OrderStatisticsDto })
  async getStatistics(@Query() query: OrderStatisticsQueryDto): Promise<OrderStatisticsDto> {
    return this.service.getStatistics(query);
  }

  @Get('trend')
  @ApiOperation({ summary: 'Lấy dữ liệu xu hướng đơn hàng & doanh thu theo ngày' })
  @ApiResponse({ status: 200, type: [OrderTrendDto] })
  async getTrend(@Query() query: OrderStatisticsQueryDto): Promise<OrderTrendDto[]> {
    return this.service.getTrend(query);
  }

  @Get('payment-methods')
  @ApiOperation({ summary: 'Lấy dữ liệu phương thức thanh toán' })
  @ApiResponse({ status: 200, type: [PaymentMethodStatsDto] })
  async getPaymentMethods(
    @Query() query: OrderStatisticsQueryDto,
  ): Promise<PaymentMethodStatsDto[]> {
    return this.service.getPaymentMethods(query);
  }

  @Get('revenue-details')
  @ApiOperation({ summary: 'Lấy chi tiết doanh thu (thực nhận, trước hoàn tiền)' })
  @ApiResponse({ status: 200, type: RevenueDetailsDto })
  async getRevenueDetails(@Query() query: OrderStatisticsQueryDto): Promise<RevenueDetailsDto> {
    return this.service.getRevenueDetails(query);
  }

  @Get('cancelled-orders')
  @ApiOperation({ summary: 'Lấy danh sách đơn hàng đã hủy trong khoảng thời gian' })
  @ApiResponse({ status: 200, type: [CancelledOrderListDto] })
  async getCancelledOrders(
    @Query() query: OrderStatisticsQueryDto,
  ): Promise<CancelledOrderListDto[]> {
    return this.service.getCancelledOrders(query);
  }
}
