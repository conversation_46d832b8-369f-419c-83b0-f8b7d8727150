import { useEffect } from 'react'
import {
  useCreateProductMutation,
  useGetShopProductsQuery,
  useGetShopProductDetailQuery,
  useUpdateProductMutation,
  useDeleteProductMutation,
  useGetShopProductInventoryQuery,
  useGetShopProductStatsQuery,
  useUploadProductImagesMutation,
  useDeleteProductImageMutation,
  useUpdateImageOrderMutation,
} from '../../services/M2/ProductManagement/ProductShop/productShop'
import { useToast } from '../../components/Toast'
import { useLoadingStore } from '@/utils/loadingStore'

// Hook lấy danh sách sản phẩm shop
const useGetShopProductsHook = (params: any) => {
  const { data, error, isLoading, refetch, isFetching } = useGetShopProductsQuery(params)
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook lấy chi tiết sản phẩm shop
const useGetShopProductDetailHook = (id: string) => {
  const { data, error, isLoading, refetch, isFetching } = useGetShopProductDetailQuery(id, {
    skip: !id,
    refetchOnMountOrArgChange: true
  })
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook lấy inventory sản phẩm shop
const useGetShopProductInventoryHook = (id: string, versionId?: string) => {
  const { data, error, isLoading, refetch, isFetching } = useGetShopProductInventoryQuery(
    { id, version_id: versionId },
    { skip: !id }
  )
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook lấy thống kê sản phẩm shop
const useGetShopProductStatsHook = (id: string) => {
  const { data, error, isLoading, refetch, isFetching } = useGetShopProductStatsQuery(id, {
    skip: !id
  })
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook tạo sản phẩm mới
const useCreateProductHook = () => {
  const { showSuccess, showError } = useToast()
  const [createFunc] = useCreateProductMutation()

  const create = async (productData: any): Promise<boolean> => {
    try {
      const res = await createFunc(productData)
      if (!res?.error) {
        showSuccess('Tạo sản phẩm thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Tạo sản phẩm thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Tạo sản phẩm thất bại' + e)
      return false
    }
  }

  return create
}

// Hook cập nhật sản phẩm
const useUpdateProductHook = () => {
  const { showSuccess, showError } = useToast()
  const [updateFunc] = useUpdateProductMutation()

  const update = async (id: string, productData: any): Promise<boolean> => {
    try {
      const res = await updateFunc({ id, data: productData })
      if (!res?.error) {
        showSuccess('Cập nhật sản phẩm thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Cập nhật sản phẩm thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Cập nhật sản phẩm thất bại' + e)
      return false
    }
  }

  return update
}

// Hook xóa sản phẩm
const useDeleteProductHook = () => {
  const { showSuccess, showError } = useToast()
  const [deleteFunc] = useDeleteProductMutation()

  const deleteProduct = async (id: string): Promise<boolean> => {
    try {
      const res = await deleteFunc(id)
      if (!res?.error) {
        showSuccess('Xóa sản phẩm thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Xóa sản phẩm thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Xóa sản phẩm thất bại' + e)
      return false
    }
  }

  return deleteProduct
}

// Hook upload hình ảnh sản phẩm
const useUploadProductImagesHook = () => {
  const { showSuccess, showError } = useToast()
  const [uploadFunc] = useUploadProductImagesMutation()

  const upload = async (files: FileList): Promise<any> => {
    const formData = new FormData()
    Array.from(files).forEach((file) => {
      formData.append('images', file)
    })

    try {
      const res = await uploadFunc(formData)
      if (!res?.error) {
        showSuccess('Upload hình ảnh thành công')
        return res.data
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Upload hình ảnh thất bại ' + messageDes)
        return null
      }
    } catch (e) {
      showError('Upload hình ảnh thất bại' + e)
      return null
    }
  }

  return upload
}

// Hook xóa hình ảnh sản phẩm
const useDeleteProductImageHook = () => {
  const { showSuccess, showError } = useToast()
  const [deleteFunc] = useDeleteProductImageMutation()

  const deleteImage = async (imageId: string): Promise<boolean> => {
    try {
      const res = await deleteFunc(imageId)
      if (!res?.error) {
        showSuccess('Xóa hình ảnh thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Xóa hình ảnh thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Xóa hình ảnh thất bại' + e)
      return false
    }
  }

  return deleteImage
}

// Hook cập nhật thứ tự hình ảnh
const useUpdateImageOrderHook = () => {
  const { showSuccess, showError } = useToast()
  const [updateFunc] = useUpdateImageOrderMutation()

  const updateOrder = async (productId: string, imageOrders: any[]): Promise<boolean> => {
    try {
      const res = await updateFunc({ productId, imageOrders })
      if (!res?.error) {
        showSuccess('Cập nhật thứ tự hình ảnh thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Cập nhật thứ tự hình ảnh thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Cập nhật thứ tự hình ảnh thất bại' + e)
      return false
    }
  }

  return updateOrder
}

// Xuất ra
export {
  useGetShopProductsHook,
  useGetShopProductDetailHook,
  useGetShopProductInventoryHook,
  useGetShopProductStatsHook,
  useCreateProductHook,
  useUpdateProductHook,
  useDeleteProductHook,
  useUploadProductImagesHook,
  useDeleteProductImageHook,
  useUpdateImageOrderHook,
}
