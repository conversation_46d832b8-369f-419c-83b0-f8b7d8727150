import { createApi } from '@reduxjs/toolkit/query/react'
import { axiosBaseQuery } from '../../../../libs/axios/axiosBase'

const baseUrl = 'http://localhost:8080'

export const productAdminApi = createApi({
  reducerPath: 'productAdminApi',
  baseQuery: axiosBaseQuery({ baseUrl }),
  tagTypes: ['ProductsAdmin', 'CategoriesAdmin'],

  endpoints: (build) => ({
    // ✅ Lấy danh sách tất cả sản phẩm (admin)
    getAdminProducts: build.query<any, any>({
      query: (params) => ({
        url: '/products', // Sử dụng endpoint hiện tại, admin có thể xem tất cả
        method: 'GET',
        params: params,
        authRequired: true,
      }),
      providesTags: ['ProductsAdmin'],
    }),

    // ✅ L<PERSON>y chi tiết sản phẩm (admin)
    getAdminProductDetail: build.query<any, string>({
      query: (id: string) => ({
        url: `/products/${id}`, // Sử dụng endpoint hiện tại
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['ProductsAdmin'],
    }),

    // ✅ Cập nhật sản phẩm (admin)
    updateAdminProduct: build.mutation<any, { id: string; data: any }>({
      query: ({ id, data }) => ({
        url: `/products/${id}`, // Sử dụng endpoint hiện tại
        method: 'PUT',
        data: data,
        authRequired: true,
      }),
      invalidatesTags: ['ProductsAdmin'],
    }),

    // ✅ Xóa sản phẩm (admin)
    deleteAdminProduct: build.mutation<any, string>({
      query: (id: string) => ({
        url: `/products/${id}`, // Sử dụng endpoint hiện tại
        method: 'DELETE',
        authRequired: true,
      }),
      invalidatesTags: ['ProductsAdmin'],
    }),

    // ✅ Lấy thống kê sản phẩm (admin)
    getAdminProductStats: build.query<any, string>({
      query: (id: string) => ({
        url: `/products/${id}/stats`, // Sử dụng endpoint hiện tại
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['ProductsAdmin'],
    }),

    // ✅ Lấy danh sách categories (admin)
    getAdminCategories: build.query<any, any>({
      query: (params) => ({
        url: '/categories', // Sử dụng endpoint hiện tại
        method: 'GET',
        params: params,
        authRequired: true,
      }),
      providesTags: ['CategoriesAdmin'],
    }),

    // ✅ Tạo category mới (admin)
    createCategory: build.mutation<any, any>({
      query: (data) => ({
        url: '/categories', // Sử dụng endpoint hiện tại
        method: 'POST',
        data: data,
        authRequired: true,
      }),
      invalidatesTags: ['CategoriesAdmin'],
    }),

    // ✅ Cập nhật category (admin)
    updateCategory: build.mutation<any, { id: string; data: any }>({
      query: ({ id, data }) => ({
        url: `/categories/${id}`, // Sử dụng endpoint hiện tại
        method: 'PUT',
        data: data,
        authRequired: true,
      }),
      invalidatesTags: ['CategoriesAdmin'],
    }),

    // ✅ Xóa category (admin)
    deleteCategory: build.mutation<any, string>({
      query: (id: string) => ({
        url: `/categories/${id}`, // Sử dụng endpoint hiện tại
        method: 'DELETE',
        authRequired: true,
      }),
      invalidatesTags: ['CategoriesAdmin'],
    }),

    // ✅ Upload icon cho category (admin)
    uploadCategoryIcon: build.mutation<any, FormData>({
      query: (formData: FormData) => ({
        url: '/categories/upload-icon', // Sử dụng endpoint hiện tại
        method: 'POST',
        data: formData,
        authRequired: true,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }),
      invalidatesTags: ['CategoriesAdmin'],
    }),
  }),
})

export const {
  useGetAdminProductsQuery,
  useGetAdminProductDetailQuery,
  useUpdateAdminProductMutation,
  useDeleteAdminProductMutation,
  useGetAdminProductStatsQuery,
  useGetAdminCategoriesQuery,
  useCreateCategoryMutation,
  useUpdateCategoryMutation,
  useDeleteCategoryMutation,
  useUploadCategoryIconMutation,
} = productAdminApi
