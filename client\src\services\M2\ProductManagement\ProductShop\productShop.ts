import { createApi } from '@reduxjs/toolkit/query/react'
import { axiosBaseQuery } from '../../../../libs/axios/axiosBase'

const baseUrl = 'http://localhost:8080'

export const productShopApi = createApi({
  reducerPath: 'productShopApi',
  baseQuery: axiosBaseQuery({ baseUrl }),
  tagTypes: ['ProductsShop', 'ProductImages'],

  endpoints: (build) => ({
    // ✅ Tạo sản phẩm mới (shop) - sử dụng endpoint hiện tại
    createProduct: build.mutation<any, any>({
      query: (data) => ({
        url: '/products', // Sử dụng endpoint hiện tại
        method: 'POST',
        data: data,
        authRequired: true,
      }),
      invalidatesTags: ['ProductsShop'],
    }),

    // ✅ Lấy danh sách sản phẩm của shop
    getShopProducts: build.query<any, any>({
      query: (params) => ({
        url: '/products', // Sử dụng endpoint hiện tại, server sẽ filter theo shop
        method: 'GET',
        params: params,
        authRequired: true,
      }),
      providesTags: ['ProductsShop'],
    }),

    // ✅ Lấy chi tiết sản phẩm của shop
    getShopProductDetail: build.query<any, string>({
      query: (id: string) => ({
        url: `/products/${id}`, // Sử dụng endpoint hiện tại
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['ProductsShop'],
    }),

    // ✅ Cập nhật sản phẩm (shop)
    updateProduct: build.mutation<any, { id: string; data: any }>({
      query: ({ id, data }) => ({
        url: `/products/${id}`, // Sử dụng endpoint hiện tại
        method: 'PUT',
        data: data,
        authRequired: true,
      }),
      invalidatesTags: ['ProductsShop'],
    }),

    // ✅ Xóa sản phẩm (shop)
    deleteProduct: build.mutation<any, string>({
      query: (id: string) => ({
        url: `/products/${id}`, // Sử dụng endpoint hiện tại
        method: 'DELETE',
        authRequired: true,
      }),
      invalidatesTags: ['ProductsShop'],
    }),

    // ✅ Lấy thông tin inventory cho sản phẩm của shop
    getShopProductInventory: build.query<any, { id: string; version_id?: string }>({
      query: ({ id, version_id }) => ({
        url: `/products/${id}/inventory${version_id ? `?version_id=${version_id}` : ''}`, // Sử dụng endpoint hiện tại
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['ProductsShop'],
    }),

    // ✅ Lấy thống kê sản phẩm của shop
    getShopProductStats: build.query<any, string>({
      query: (id: string) => ({
        url: `/products/${id}/stats`, // Sử dụng endpoint hiện tại
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['ProductsShop'],
    }),

    // ✅ Upload ảnh sản phẩm (shop)
    uploadProductImages: build.mutation<any, FormData>({
      query: (formData: FormData) => ({
        url: '/product-images/upload', // Sử dụng endpoint hiện tại
        method: 'POST',
        data: formData,
        authRequired: true,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }),
      invalidatesTags: ['ProductImages'],
    }),

    // ✅ Xóa ảnh sản phẩm (shop)
    deleteProductImage: build.mutation<any, string>({
      query: (imageId: string) => ({
        url: `/product-images/${imageId}`, // Sử dụng endpoint hiện tại
        method: 'DELETE',
        authRequired: true,
      }),
      invalidatesTags: ['ProductImages'],
    }),

    // ✅ Cập nhật thứ tự ảnh sản phẩm (shop)
    updateImageOrder: build.mutation<any, { productId: string; imageOrders: any[] }>({
      query: ({ productId, imageOrders }) => ({
        url: `/product-images/${productId}/order`, // Sử dụng endpoint hiện tại
        method: 'PUT',
        data: { imageOrders },
        authRequired: true,
      }),
      invalidatesTags: ['ProductImages'],
    }),
  }),
})

export const {
  useCreateProductMutation,
  useGetShopProductsQuery,
  useGetShopProductDetailQuery,
  useUpdateProductMutation,
  useDeleteProductMutation,
  useGetShopProductInventoryQuery,
  useGetShopProductStatsQuery,
  useUploadProductImagesMutation,
  useDeleteProductImageMutation,
  useUpdateImageOrderMutation,
} = productShopApi
