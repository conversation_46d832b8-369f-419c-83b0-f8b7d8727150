import React, { useState, useEffect, useMemo } from 'react';
import { useParams } from 'react-router-dom';
import { FaHeart, FaRegHeart, FaStar, FaRegStar, FaShareAlt, FaShoppingCart, FaQrcode } from 'react-icons/fa';
import { IoChevronBack, IoChevronForward, IoClose } from 'react-icons/io5';
import { MdZoomIn } from 'react-icons/md';
import { useGetProductDetailHook } from '@/hooks/product/useproduct';

import { useAddToCartHook } from '@/hooks/cart/useCart';
import { useNavigate, useLocation } from 'react-router-dom';
import { useGetFavoritesHook, useToggleFavoriteHook } from '@/hooks/product/useproduct';

import { useGetMyReviewsHook, useGetToReviewHook } from '@/hooks/product/useProductReview';
import { useGetProductInventoryHook, useGetProductStatsHook } from '@/hooks/product/useproduct';
import { useSelector } from 'react-redux';
import type { RootState } from '@/libs/state/store';
import { useToast } from '@/components/Toast';
import styles from './ProductDetail.module.scss';
import logo from '@/assets/images/logo.png';
import { getAvatarProps } from '@/utils/avatar';
import type {
  ProductVersion as ServiceProductVersion,
  ProductImage as ServiceProductImage,
  ProductAttribute as ServiceProductAttribute,
} from '@/services/M2/ProductManagement/productManagement';

interface ProductImage extends Partial<ServiceProductImage> {
  id?: string;
  image_id?: string;
  display_order?: number;
}

interface ProductVersion extends Partial<ServiceProductVersion> {
  id?: string;
  image_url?: string | null;
}

interface ProductAttribute extends Partial<ServiceProductAttribute> {
  id?: string;
  name?: string;
  value?: string;
  unit?: string;
  is_searchable?: boolean;
}

interface ProductData {
  id: string;
  user_id: string;
  name: string;
  slug: string;
  description: string;
  category_id: string;
  price: string;
  unit: string;
  stock: number;
  status_id: string;
  is_public: boolean;
  seo_keywords: string | null;
  thumbnail_url: string;
  source: string | null;
  created_at: string;
  updated_at: string;
  product_images: ProductImage[];
  product_versions: ProductVersion[];
  product_attribute: ProductAttribute[];
  product_qr_code: {
    id: string;
    product_id: string;
    qr_url: string;
    target_url: string;
    type: string;
    scan_count: number;
    created_at: string;
  };
}

interface Comment {
  id: string;
  user_name: string;
  user_avatar: string;
  rating: number;
  comment: string;
  created_at: string;
}

const ProductDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [selectedVersion, setSelectedVersion] = useState<ProductVersion | null>(null);
  const [isFavorited, setIsFavorited] = useState(false);
  const [quantity, setQuantity] = useState(1);
  const [showImageModal, setShowImageModal] = useState(false);

  const [favoritesLoaded, setFavoritesLoaded] = useState(false);

  // Sử dụng hooks thực tế
  const { data: apiResponse, isLoading, error, refetch } = useGetProductDetailHook(id || '');
  const navigate = useNavigate();
  const location = useLocation();
  const auth = useSelector((state: RootState) => state.auth);
  const { showInfo, showSuccess, showError } = useToast();

  // Stable auth check để tránh lỗi khi logout
  const isAuthenticated = useMemo(() => !!auth?.id, [auth?.id]);

  // Chỉ sử dụng các hook yêu cầu auth khi user đã đăng nhập
  const { data: favoritesData, refetch: refetchFavorites } = isAuthenticated
    ? useGetFavoritesHook()
    : { data: [], refetch: () => {} };

  const toggleFavorite = isAuthenticated
    ? useToggleFavoriteHook()
    : () => Promise.resolve(null);



  const addToCart = isAuthenticated
    ? useAddToCartHook()
    : () => Promise.resolve(null);

  // Hook lấy đánh giá sản phẩm thực tế - chỉ khi đã đăng nhập
  const { data: myReviewsData, isLoading: isLoadingMyReviews, refetch: refetchMyReviews } = isAuthenticated
    ? useGetMyReviewsHook()
    : { data: [], isLoading: false, refetch: () => {} };
  
  // SỬA: Lấy product trực tiếp từ apiResponse
  const product = apiResponse;
  const isSuccess = !!product;
  const isError = !isSuccess;

  // Lấy thông tin inventory cho version được chọn
  const { data: inventoryData, refetch: refetchInventory } = useGetProductInventoryHook({
    productId: product?.id || '',
    versionId: selectedVersion?.id,
  });

  // Lấy thống kê sản phẩm (đánh giá và số lượng đã bán)
  const { data: productStats } = useGetProductStatsHook(product?.id || '');

  useEffect(() => {
    if (product?.product_versions && product.product_versions.length > 0) {
      // Nếu đã có phiên bản được chọn, giữ nguyên
      if (selectedVersion) {
        const versionStillExists = product.product_versions.some((v: any) => v.id === selectedVersion.id);
        if (versionStillExists) {
          // Phiên bản vẫn tồn tại, không cần thay đổi
          return;
        }
      }
      // Nếu chưa có phiên bản được chọn hoặc phiên bản không còn tồn tại, chọn phiên bản đầu tiên
      setSelectedVersion(product.product_versions[0] as ProductVersion | null);
    }
  }, [product, selectedVersion]);

  useEffect(() => {
    if (favoritesData && product?.id) {
      const favoritesArray = Array.isArray(favoritesData)
        ? favoritesData
        : ((favoritesData as { data?: any[] })?.data || []);
      const isFav = favoritesArray.some((fav: any) => fav.product_id === product.id);
      setIsFavorited(isFav);
      setFavoritesLoaded(true);
    } else if (favoritesData && !product?.id) {
      // Reset state nếu không có product ID
      setIsFavorited(false);
      setFavoritesLoaded(true);
    }
  }, [favoritesData, product?.id]);

  const handleImageNavigation = (direction: 'prev' | 'next') => {
    if (!product?.product_images || product.product_images.length === 0) return;
    
    const totalImages = product.product_images.length;
    if (direction === 'prev') {
      setCurrentImageIndex((prev) => (prev - 1 + totalImages) % totalImages);
    } else {
      setCurrentImageIndex((prev) => (prev + 1) % totalImages);
    }
  };

  const handleVersionSelect = (version: ProductVersion) => {
    setSelectedVersion(version as ProductVersion | null);
    // Tìm ảnh tương ứng với phiên bản
    const versionImageIndex = (product?.product_images ?? []).findIndex((img: ProductImage) => (img?.image_url ?? '') === (version?.image_url ?? ''));
    if (versionImageIndex !== -1 && versionImageIndex !== undefined) {
      setCurrentImageIndex(versionImageIndex);
    }
    // Refetch inventory cho version mới
    setTimeout(() => refetchInventory(), 100);
  };

  const handleAddToCart = async () => {
    if (!isAuthenticated) {
      showInfo('Vui lòng đăng nhập để thêm sản phẩm vào giỏ hàng');
      navigate('/dang-nhap', { state: { from: location.pathname } });
      return;
    }

    if (!product || !selectedVersion) return;
    const success = await addToCart({
      product_id: product.id,
      quantity: quantity,
      unit_price: Number(selectedVersion.price || product.price),
      version_id: selectedVersion.id || '',
      selected_options: {},
    });
    if (success) {
      // Cart sẽ tự động refetch nhờ RTK Query invalidatesTags
      // Thông báo đã được hiển thị trong useAddToCartHook
    }
  };

  const handleBuyNow = async () => {
    if (!isAuthenticated) {
      showInfo('Vui lòng đăng nhập để mua sản phẩm');
      navigate('/dang-nhap', { state: { from: location.pathname } });
      return;
    }

    if (!product || !selectedVersion) return;
    
    // Kiểm tra sản phẩm đã có trong giỏ hàng chưa
    // cartItems removed since cartData is not available
    const versionId = selectedVersion.id || '';
    // cartItems removed, so always add to cart first then navigate
    // No need to check if item exists in cart
    
    // Nếu chưa có thì thêm vào giỏ rồi chuyển sang thanh toán
    const success = await addToCart({
      product_id: product.id,
      quantity: quantity,
      unit_price: Number(selectedVersion.price || product.price),
      version_id: selectedVersion.id || '',
      selected_options: {},
    });
    if (success) {

      // Tạo dữ liệu đơn hàng tạm thời và chuyển sang trang thanh toán
      const orderItems = [{
        product_id: product.id,
        version_id: selectedVersion.id || '',
        quantity: quantity,
        unit_price: Number(selectedVersion.price || product.price),
        name: product.name,
        thumbnail_url: product.thumbnail_url,
      }];
      const totals = {
        subtotal: Number(selectedVersion.price || product.price) * quantity,
        discount: 0,
        shipping: 25000,
        total: Number(selectedVersion.price || product.price) * quantity + 25000,
      };
      localStorage.setItem('checkoutData', JSON.stringify({ items: orderItems, totals }));
      navigate('/gio-hang/chi-tiet-don-hang', {
        state: {
          items: orderItems,
          totals,
        },
      });
    }
  };



  const handleShare = () => {
    if (navigator.share && product) {
      navigator.share({
        title: product.name,
        text: product.description,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      showInfo('Đã copy link sản phẩm!');
    }
  };

  const handleToggleFavorite = async () => {
    if (!isAuthenticated) {
      showInfo('Vui lòng đăng nhập để thêm vào yêu thích');
      navigate('/dang-nhap', { state: { from: location.pathname } });
      return;
    }

    if (!product?.id || !favoritesLoaded) return;
    
    // Lưu trạng thái hiện tại để rollback nếu cần
    const currentState = isFavorited;
    const newState = !currentState;
    
    try {
      // Optimistic update - cập nhật UI ngay
      setIsFavorited(newState);
      
      // Gọi API toggle
      const success = await toggleFavorite(product.id, currentState);
      
      if (success) {
        // Refetch để đồng bộ với server
        await refetchFavorites();
      } else {
        // Rollback nếu API thất bại
        setIsFavorited(currentState);
      }
      
    } catch (error) {
      // Rollback nếu có lỗi
      setIsFavorited(currentState);
    }
  };
  
  

  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <span key={i} className={styles.star}>
          {i <= rating ? <FaStar /> : <FaRegStar />}
        </span>
      );
    }
    return stars;
  };

  const formatPrice = (price: string) => {
    return new Intl.NumberFormat('vi-VN').format(parseInt(price));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // ✅ SỬA LỖI: Kiểm tra loading state trước
  if (isLoading) {
    return <div className={styles.loading}>Đang tải...</div>;
  }

  // ✅ SỬA LỖI: Chỉ hiển thị lỗi khi không có data hoặc statusCode khác 200
  if (isError) {
    return (
      <div className={styles.error}>
        <h2>Không tìm thấy sản phẩm</h2>
        <p>Sản phẩm bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.</p>
        <button onClick={() => window.history.back()}>Quay lại</button>
      </div>
    );
  }

  // Lọc đánh giá cho sản phẩm hiện tại
  const productReviews = Array.isArray(myReviewsData)
    ? myReviewsData.filter((rv: any) => rv.product_id === product?.id)
    : Array.isArray((myReviewsData as any)?.data)
    ? (myReviewsData as any).data.filter((rv: any) => rv.product_id === product?.id)
    : [];

  // Sử dụng thống kê từ productStats thay vì tính toán từ myReviewsData
  const averageRating = productStats?.average_rating || 0;
  const totalReviews = productStats?.total_reviews || 0;

  // ✅ SỬA LỖI: Xử lý fallback cho product_images rỗng
  const displayImages: ProductImage[] = (product.product_images && product.product_images.length > 0)
    ? (product.product_images as ProductImage[])
    : [{ id: 'thumbnail', image_url: String(product.thumbnail_url ?? ''), image_id: 'thumb', display_order: 0, public_image_id: '', is_thumbnail: true }];

  return (
    <div className={styles.productDetailPage}>
      <div className={styles.container}>
        {/* Breadcrumb */}
        <nav className={styles.breadcrumb}>
          <a href="/">Trang chủ</a>
          <span>/</span>
          <a href="/products">Sản phẩm</a>
          <span>/</span>
          <span>{product.name}</span>
        </nav>

        {/* Product Main Section */}
        <div className={styles.productMain}>
          {/* Product Images */}
          <div className={styles.productImages}>
            <div className={styles.imageGallery}>
              <div className={styles.thumbnailList}>
                {displayImages.map((image, index) => (
                  <div
                    key={image.id || index}
                    className={`${styles.thumbnailItem} ${index === currentImageIndex ? styles.active : ''}`}
                    onClick={() => setCurrentImageIndex(index)}
                  >
                    <img
                      src={(String(image.image_url ?? '') || '')}
                      alt={String(product?.name ?? '') || ''}
                    />
                  </div>
                ))}
              </div>
              
              <div className={styles.mainImageContainer}>
                <img
                  src={(String(displayImages[currentImageIndex]?.image_url || '') || String(product.thumbnail_url || '') || logo)}
                  alt={String(product?.name ?? '') || ''}
                  className={styles.mainImage}
                />
                
                <button
                  className={styles.zoomButton}
                  onClick={() => setShowImageModal(true)}
                >
                  <MdZoomIn />
                </button>
                
                {displayImages.length > 1 && (
                  <>
                    <button
                      className={`${styles.imageNavButton} ${styles.prevButton}`}
                      onClick={() => handleImageNavigation('prev')}
                    >
                      <IoChevronBack />
                    </button>
                    <button
                      className={`${styles.imageNavButton} ${styles.nextButton}`}
                      onClick={() => handleImageNavigation('next')}
                    >
                      <IoChevronForward />
                    </button>
                  </>
                )}
                
                <div className={styles.imageCounter}>
                  {currentImageIndex + 1}/{displayImages.length}
                </div>
              </div>
            </div>
          </div>

          {/* Product Info */}
          <div className={styles.productInfo}>
            <div className={styles.productHeader}>
              <h1 className={styles.productName}>{product.name}</h1>
              
              <div className={styles.productRating}>
                <div className={styles.ratingLeft}>
                  <div className={styles.stars}>
                    {renderStars(Math.round(productStats?.average_rating || 0))}
                  </div>
                  <span className={styles.ratingText}>
                    {(productStats?.average_rating || 0).toFixed(1)}
                  </span>
                </div>
                <div className={styles.ratingRight}>
                  <span className={styles.reviewCount}>
                    {productStats?.total_reviews || 0} Đánh Giá
                  </span>
                  <span className={styles.soldCount}>
                    {productStats?.total_sold || 0} Đã Bán
                  </span>
                </div>
              </div>
            </div>

            <div className={styles.priceSection}>
              <div className={styles.priceContainer}>
                <span className={styles.currentPrice}>₫{formatPrice(String(selectedVersion?.price ?? product.price ?? '0'))}</span>
                <span className={styles.unit}>/{product.unit}</span>
              </div>
            </div>

            {/* Product Versions */}
            {product.product_versions && product.product_versions.length > 0 && (
              <div className={styles.variantSection}>
                <h3>Phiên Bản</h3>
                <div className={styles.variantGrid}>
                  {(product.product_versions as ProductVersion[]).map((version, idx) => (
                    <div
                      key={version.id || idx}
                      className={`${styles.variantItem} ${
                        selectedVersion?.id === version.id ? styles.active : ''
                      }`}
                      onClick={() => handleVersionSelect(version)}
                    >
                      <img 
                        src={String(version.image_url ?? product.thumbnail_url ?? '') || logo} 
                        alt={String(version.name ?? '')}
                        className={styles.variantImage}
                      />
                      <div className={styles.variantInfo}>
                        <span className={styles.variantName}>{version.name ?? ''}</span>
                        <span className={styles.variantPrice}>₫{formatPrice(String(version.price ?? '0'))}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Quantity */}
            <div className={styles.quantitySection}>
              <h3>Số Lượng</h3>
              <div className={styles.quantityControls}>
                <div className={styles.quantitySelector}>
                  <button
                    className={styles.quantityButton}
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  >
                    -
                  </button>
                  <input
                    type="number"
                    value={quantity}
                    onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                    className={styles.quantityInput}
                  />
                  <button
                    className={styles.quantityButton}
                    onClick={() => setQuantity(quantity + 1)}
                  >
                    +
                  </button>
                </div>
                <span className={styles.stockInfo}>
                  {(() => {
                    const available = inventoryData?.available || 0;
                    const stockQuantity = inventoryData?.stock_quantity || 0;
                    const reservedQuantity = inventoryData?.reserved_quantity || 0;
                    
                    // Fallback: nếu không có inventory data, sử dụng product.stock
                    if (!inventoryData && product) {
                      if (selectedVersion) {
                        // Nếu có version, lấy stock từ version
                        return `${selectedVersion.stock || 0} ${product.unit} có sẵn`;
                      } else {
                        // Nếu không có version, lấy từ product
                        return `${product.stock || 0} ${product.unit} có sẵn`;
                      }
                    }
                    
                    return `${available} ${product.unit} có sẵn`;
                  })()}
                </span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className={styles.actionSection}>
              <div className={styles.actionButtons}>
                <button
                  className={styles.addToCartButton}
                  onClick={handleAddToCart}
                  disabled={(() => {
                    // Nếu có inventory data, sử dụng available
                    if (inventoryData) {
                      return (inventoryData.available || 0) <= 0;
                    }
                    // Fallback: nếu không có inventory data, kiểm tra stock
                    if (product) {
                      if (selectedVersion) {
                        return (selectedVersion.stock || 0) <= 0;
                      } else {
                        return (product.stock || 0) <= 0;
                      }
                    }
                    return true; // Disable nếu không có data
                  })()}
                >
                  <FaShoppingCart />
                  Thêm Vào Giỏ Hàng
                </button>
                
                <button
                  className={styles.buyNowButton}
                  onClick={handleBuyNow}
                  disabled={(() => {
                    // Nếu có inventory data, sử dụng available
                    if (inventoryData) {
                      return (inventoryData.available || 0) <= 0;
                    }
                    // Fallback: nếu không có inventory data, kiểm tra stock
                    if (product) {
                      if (selectedVersion) {
                        return (selectedVersion.stock || 0) <= 0;
                      } else {
                        return (product.stock || 0) <= 0;
                      }
                    }
                    return true; // Disable nếu không có data
                  })()}
                >
                  Mua Ngay
                </button>
              </div>
              
              <div className={styles.secondaryActions}>
                <button
                  className={`${styles.favoriteButton} ${isFavorited ? styles.favorited : ''}`}
                  onClick={handleToggleFavorite}
                  aria-label={isFavorited ? 'Bỏ khỏi yêu thích' : 'Thêm vào yêu thích'}
                  disabled={isAuthenticated ? (!favoritesLoaded || !product?.id) : false}
                >
                  {isFavorited ? <FaHeart /> : <FaRegHeart />}
                </button>
                
                <button 
                  className={styles.shareButton}
                  onClick={handleShare}
                >
                  <FaShareAlt />
                </button>
              </div>
            </div>

            {/* QR Code */}
            {product.product_qr_code && (
              <div className={styles.qrSection}>
                <h3>Mã QR Sản Phẩm</h3>
                <div className={styles.qrCode}>
                  <img
                    src={`https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${product.product_qr_code.qr_url}`}
                    alt="QR Code"
                  />
                  <p>Quét mã QR để xem sản phẩm</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Product Attributes */}
        {product.product_attribute && product.product_attribute.length > 0 && (
          <div className={styles.productAttributes}>
            <h2>Thông Tin Sản Phẩm</h2>
            <div className={styles.attributeGrid}>
              {(product.product_attribute as ProductAttribute[]).map((attr, idx) => (
                <div key={attr.id || idx} className={styles.attributeItem}>
                  <span className={styles.attributeName}>{attr.name ?? ''}</span>
                  <span className={styles.attributeValue}>{attr.value ?? ''}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Product Description */}
        <div className={styles.productDescription}>
          <h2>Mô Tả Sản Phẩm</h2>
          <div className={styles.descriptionContent}>
            <p>{product.description || 'Chưa có mô tả cho sản phẩm này.'}</p>
          </div>
        </div>

        {/* Comments Section */}
        <div className={styles.commentsSection}>
          <h2>Đánh Giá Sản Phẩm</h2>
          <div className={styles.ratingOverview}>
            <div className={styles.ratingScore}>
              <span className={styles.scoreNumber}>{averageRating.toFixed(1)}</span>
              <div className={styles.scoreStars}>
                {renderStars(Math.round(averageRating))}
              </div>
              <span className={styles.totalReviews}>({totalReviews} đánh giá)</span>
            </div>
          </div>

          {/* Comments List */}
          <div className={styles.commentsList}>
            {isLoadingMyReviews ? (
              <div>Đang tải đánh giá...</div>
            ) : (productStats?.reviews && productStats.reviews.length > 0) ? (
              productStats.reviews.map((review: any, index: number) => {
                // Tạo tên hiển thị từ thông tin user
                const userName = review.user?.profile?.fullName 
                  ? review.user.profile.fullName
                  : review.user?.email?.split('@')[0] || 'Người dùng';
                
                return (
                  <div key={index} className={styles.commentItem}>
                    <div className={styles.commentHeader}>
                      <img
                        {...getAvatarProps(review.user?.profile?.avatarUrl, userName)}
                        className={styles.userAvatar}
                      />
                      <div className={styles.commentMeta}>
                        <h4>{userName}</h4>
                        <div className={styles.commentRating}>
                          {renderStars(review.rating || 0)}
                        </div>
                        <span className={styles.commentDate}>
                          {formatDate(review.created_at)}
                        </span>
                      </div>
                    </div>
                    <p className={styles.commentText}>{review.content || 'Không có nội dung'}</p>
                  </div>
                );
              })
            ) : (
              <div>Chưa có đánh giá nào cho sản phẩm này.</div>
            )}
          </div>
        </div>
      </div>

      {/* Image Modal */}
      {showImageModal && (
        <div className={styles.imageModal} onClick={() => setShowImageModal(false)}>
          <div className={styles.modalContent}>
            <button
              className={styles.closeModal}
              onClick={() => setShowImageModal(false)}
            >
              <IoClose />
            </button>
            <img
              src={(String(displayImages[currentImageIndex]?.image_url || '') || String(product.thumbnail_url || '') || logo)}
              alt={String(product?.name ?? '') || ''}
              className={styles.modalImage}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductDetail; 