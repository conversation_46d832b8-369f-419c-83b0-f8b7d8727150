import { useEffect } from 'react'
import {
  useGetAdminOrdersQuery,
  useGetAdminOrderDetailQuery,
  useUpdateAdminOrderStatusMutation,
  useGetAdminCancelOrderRequestsQuery,
  useAcceptAdminCancelRequestMutation,
  type OrderManagementListParams,
  type UpdateOrderStatusRequest,
  type CancelRequestListParams,
} from '../../services/M2/OrderManagement/OrderAdmin/orderAdmin'
import { useToast } from '../../components/Toast'
import { useLoadingStore } from '@/utils/loadingStore'

// Hook lấy danh sách đơn hàng admin
const useGetAdminOrdersHook = (params: OrderManagementListParams) => {
  const { data, error, isLoading, refetch, isFetching } = useGetAdminOrdersQuery(params)
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook lấy chi tiết đơn hàng admin
const useGetAdminOrderDetailHook = (id: string) => {
  const { data, error, isLoading, refetch, isFetching } = useGetAdminOrderDetailQuery(id, {
    skip: !id,
    refetchOnMountOrArgChange: true
  })
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook lấy danh sách yêu cầu hủy đơn hàng admin
const useGetAdminCancelOrderRequestsHook = (params: CancelRequestListParams) => {
  const { data, error, isLoading, refetch, isFetching } = useGetAdminCancelOrderRequestsQuery(params)
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook cập nhật trạng thái đơn hàng admin
const useUpdateAdminOrderStatusHook = () => {
  const { showSuccess, showError } = useToast()
  const [updateFunc] = useUpdateAdminOrderStatusMutation()

  const update = async (id: string, statusId: string): Promise<boolean> => {
    try {
      const data: UpdateOrderStatusRequest = { status_id: statusId }
      const res = await updateFunc({ id, data })
      if (!res?.error) {
        showSuccess('Cập nhật trạng thái đơn hàng thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Cập nhật trạng thái thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Cập nhật trạng thái thất bại' + e)
      return false
    }
  }

  return update
}

// Hook chấp nhận yêu cầu hủy đơn hàng admin
const useAcceptAdminCancelRequestHook = () => {
  const { showSuccess, showError } = useToast()
  const [acceptFunc] = useAcceptAdminCancelRequestMutation()

  const accept = async (id: string): Promise<boolean> => {
    try {
      const res = await acceptFunc(id)
      if (!res?.error) {
        showSuccess('Chấp nhận yêu cầu hủy đơn hàng thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Chấp nhận yêu cầu hủy thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Chấp nhận yêu cầu hủy thất bại' + e)
      return false
    }
  }

  return accept
}

// Xuất ra
export {
  useGetAdminOrdersHook,
  useGetAdminOrderDetailHook,
  useGetAdminCancelOrderRequestsHook,
  useUpdateAdminOrderStatusHook,
  useAcceptAdminCancelRequestHook,
}
