import { useEffect } from 'react'
import {
  useGetPublicProductsQuery,
  useGetPublicProductDetailQuery,
  useGetPublicProductInventoryQuery,
  useGetPublicProductStatsQuery,
  useSearchProductsByImageMutation,
  useGetPublicCategoriesQuery,
} from '../../services/M2/ProductManagement/ProductPublic/productPublic'
import { useToast } from '../../components/Toast'
import { useLoadingStore } from '@/utils/loadingStore'

// Hook lấy danh sách sản phẩm public
const useGetPublicProductsHook = (params: any) => {
  const { data, error, isLoading, refetch, isFetching } = useGetPublicProductsQuery(params)
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook lấy chi tiết sản phẩm public
const useGetPublicProductDetailHook = (id: string) => {
  const { data, error, isLoading, refetch, isFetching } = useGetPublicProductDetailQuery(id, {
    skip: !id,
    refetchOnMountOrArgChange: true
  })
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook lấy inventory sản phẩm public
const useGetPublicProductInventoryHook = (id: string, versionId?: string) => {
  const { data, error, isLoading, refetch, isFetching } = useGetPublicProductInventoryQuery(
    { id, version_id: versionId },
    { skip: !id }
  )
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook lấy thống kê sản phẩm public
const useGetPublicProductStatsHook = (id: string) => {
  const { data, error, isLoading, refetch, isFetching } = useGetPublicProductStatsQuery(id, {
    skip: !id
  })
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook lấy danh mục public
const useGetPublicCategoriesHook = (params: any = {}) => {
  const { data, error, isLoading, refetch, isFetching } = useGetPublicCategoriesQuery(params)
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook tìm kiếm sản phẩm bằng hình ảnh
const useSearchProductsByImageHook = () => {
  const { showSuccess, showError } = useToast()
  const [searchFunc] = useSearchProductsByImageMutation()

  const search = async (imageFile: File): Promise<any> => {
    try {
      const res = await searchFunc(imageFile)
      if (!res?.error) {
        showSuccess('Tìm kiếm bằng hình ảnh thành công')
        return res.data
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Tìm kiếm thất bại ' + messageDes)
        return null
      }
    } catch (e) {
      showError('Tìm kiếm thất bại' + e)
      return null
    }
  }

  return search
}

// Xuất ra
export {
  useGetPublicProductsHook,
  useGetPublicProductDetailHook,
  useGetPublicProductInventoryHook,
  useGetPublicProductStatsHook,
  useGetPublicCategoriesHook,
  useSearchProductsByImageHook,
}
