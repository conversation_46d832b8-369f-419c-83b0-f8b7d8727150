import { createApi } from '@reduxjs/toolkit/query/react';
import { axiosBaseQuery } from '../../../libs/axios/axiosBase';

const baseUrl = 'http://localhost:8080';

// Types cho request/response
export interface OrderManagementListParams {
  page?: number;
  size?: number;
  search?: string;
  status_id?: string;
}

export interface OrderManagementListResponse {
  data?: any;
  message?: string;
  statusCode?: number;
}

export interface OrderDetailResponse {
  data?: any;
  message?: string;
  statusCode?: number;
}

export interface UpdateOrderStatusRequest {
  status_id: string;
}

export interface CancelRequestListParams {
  page?: number;
  size?: number;
  search?: string;
  status_id?: string;
}

export interface CancelRequestListResponse {
  data?: any;
  message?: string;
  statusCode?: number;
}

export const orderManagementApi = createApi({
  reducerPath: 'orderManagementApi',
  baseQuery: axiosBaseQuery({ baseUrl }),
  tagTypes: ['OrderManagement'],
  endpoints: (build) => ({
    // Admin: Danh sách đơn hàng
    getAdminOrders: build.query<OrderManagementListResponse, OrderManagementListParams>({
      query: (params) => ({
        url: '/orders-management/admin/order',
        method: 'GET',
        params,
        authRequired: true,
      }),
      providesTags: ['OrderManagement'],
    }),
    // Admin: Chi tiết đơn hàng
    getAdminOrderDetail: build.query<OrderDetailResponse, string>({
      query: (id) => ({
        url: `/orders-management/admin/order/${id}`,
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['OrderManagement'],
    }),
    // Seller: Danh sách đơn hàng
    getSellerOrders: build.query<OrderManagementListResponse, OrderManagementListParams>({
      query: (params) => ({
        url: '/orders-management/seller',
        method: 'GET',
        params,
        authRequired: true,
      }),
      providesTags: ['OrderManagement'],
    }),
    // Admin: Cập nhật trạng thái đơn hàng
    updateOrderStatus: build.mutation<OrderDetailResponse, { id: string; data: UpdateOrderStatusRequest }>({
      query: ({ id, data }) => ({
        url: `/orders-management/${id}/status`,
        method: 'POST',
        data,
        authRequired: true,
      }),
      invalidatesTags: ['OrderManagement'],
    }),
    // Admin: Danh sách yêu cầu hủy đơn
    getCancelRequests: build.query<CancelRequestListResponse, CancelRequestListParams>({
      query: (params) => ({
        url: '/orders-management/admin/cancel-requests',
        method: 'GET',
        params,
        authRequired: true,
      }),
      providesTags: ['OrderManagement'],
    }),
    // Admin/Seller: Duyệt yêu cầu hủy đơn
    acceptCancelRequest: build.mutation<OrderDetailResponse, string>({
      query: (id) => ({
        url: `/orders-management/${id}/accept-cancel-request`,
        method: 'POST',
        authRequired: true,
      }),
      invalidatesTags: ['OrderManagement'],
    }),
    // Seller: Danh sách yêu cầu hủy đơn
    getSellerCancelRequests: build.query<CancelRequestListResponse, CancelRequestListParams>({
      query: (params) => ({
        url: '/orders-management/seller/cancel-requests',
        method: 'GET',
        params,
        authRequired: true,
      }),
      providesTags: ['OrderManagement'],
    }),
    // Seller: Chi tiết đơn hàng
    getSellerOrderDetail: build.query<OrderDetailResponse, string>({
      query: (order_id) => ({
        url: `/orders-management/seller/${order_id}`,
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['OrderManagement'],
    }),
  }),
});

export const {
  useGetAdminOrdersQuery,
  useGetAdminOrderDetailQuery,
  useGetSellerOrdersQuery,
  useUpdateOrderStatusMutation,
  useGetCancelRequestsQuery,
  useAcceptCancelRequestMutation,
  useGetSellerCancelRequestsQuery,
  useGetSellerOrderDetailQuery,
} = orderManagementApi;
