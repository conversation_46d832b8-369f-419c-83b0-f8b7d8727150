// src/constants/sidebarItems.tsx
import {
  MdOutlinePeopleAlt,
  MdDashboard,
  MdPeople,
  MdInventory2,
  MdShoppingCart,
  MdBarChart,
  MdWarehouse,
  MdCategory,
} from 'react-icons/md'
import { ROUTES } from './routes'
import type { RoleType } from '@/services/M1/auth'
import type { Role } from '@/types/components/route'
import { FaUserLock } from 'react-icons/fa'

export type SiderItem = {
  name: string
  link: string
  icon: React.ReactNode
  requiredPermissions?: string[] // 👈 Thêm dòng này để dùng với checkPermission
}

export const commonSidebarItems: SiderItem[] = [
  {
    name: 'Trang chủ',
    link: ROUTES.HOME,
    icon: <MdOutlinePeopleAlt />,
    requiredPermissions: [],
  },
]

export const permissionSidebarItems: SiderItem[] = [
  {
    name: '<PERSON>ảng điều khiển',
    link: ROUTES.DASHBOARD,
    icon: <MdDashboard />,
    requiredPermissions: ['PERM_USER_LIST'],
  },
  {
    name: 'Quản lý quyền',
    link: ROUTES.PERMISSION_MANAGEMENT,
    icon: <FaUserLock />,
    requiredPermissions: ['PERM_USER_LIST'],
  },
  {
    name: 'Quản lý người dùng',
    link: ROUTES.USER_MANAGEMENT,
    icon: <MdPeople />,
    requiredPermissions: ['PERM_USER_LIST'],
  },
  {
    name: 'Quản lý sản phẩm',
    link: ROUTES.PRODUCT_MANAGEMENT,
    icon: <MdInventory2 />,
    requiredPermissions: ['PERM_PRODUCT_MANAGE'],
  },
  {
    name: 'Quản lý đơn hàng',
    link: ROUTES.ORDER_MANAGEMENT,
    icon: <MdShoppingCart />,
    requiredPermissions: ['PERM_ORDER_VIEW_ALL'],
  },
  {
    name: 'Thống kê đơn hàng',
    link: ROUTES.ORDER_STATISTICS,
    icon: <MdBarChart />,
    requiredPermissions: ['PERM_ORDER_VIEW_ALL'],
  },
  {
    name: 'Quản lý hàng tồn kho',
    link: ROUTES.INVENTORY_MANAGEMENT,
    icon: <MdWarehouse />,
    requiredPermissions: ['PERM_INVENTORY_MANAGE'],
  },
  {
    name: 'Quản lý danh mục',
    link: ROUTES.CATEGORY_MANAGEMENT,
    icon: <MdCategory />,
    requiredPermissions: ['PERM_CATEGORY_MANAGE'],
  },
]

export const baseRedirect: Record<string, string> = {
  R01: ROUTES.PROFILE,
  R02: ROUTES.PROFILE,
  R03: ROUTES.PROFILE,
  R04: ROUTES.PROFILE,
  R05: ROUTES.USER_MANAGEMENT,
}
