import { createApi } from '@reduxjs/toolkit/query/react'
import { axiosBaseQuery } from '../../../../libs/axios/axiosBase'

const baseUrl = 'http://localhost:8080'

export const productPublicApi = createApi({
  reducerPath: 'productPublicApi',
  baseQuery: axiosBaseQuery({ baseUrl }),
  tagTypes: ['ProductsPublic', 'CategoriesPublic'],

  endpoints: (build) => ({
    // ✅ Lấy danh sách sản phẩm public (sử dụng endpoint hiện tại)
    getPublicProducts: build.query<any, any>({
      query: (params) => ({
        url: '/products', // Sử dụng endpoint hiện tại
        method: 'GET',
        params: params,
        authOptional: false, // Public endpoint, không cần token
      }),
      providesTags: ['ProductsPublic'],
    }),

    // ✅ Lấy chi tiết sản phẩm public
    getPublicProductDetail: build.query<any, string>({
      query: (id: string) => ({
        url: `/products/${id}`, // Sử dụng endpoint hiện tại
        method: 'GET',
        authOptional: false, // Public endpoint, không cần token
      }),
      providesTags: ['ProductsPublic'],
    }),

    // ✅ Lấy thông tin inventory cho sản phẩm
    getPublicProductInventory: build.query<any, { id: string; version_id?: string }>({
      query: ({ id, version_id }) => ({
        url: `/products/${id}/inventory${version_id ? `?version_id=${version_id}` : ''}`, // Sử dụng endpoint hiện tại
        method: 'GET',
        authOptional: false, // Public endpoint, không cần token
      }),
      providesTags: ['ProductsPublic'],
    }),

    // ✅ Lấy thống kê sản phẩm
    getPublicProductStats: build.query<any, string>({
      query: (id: string) => ({
        url: `/products/${id}/stats`, // Sử dụng endpoint hiện tại
        method: 'GET',
        authOptional: false, // Public endpoint, không cần token
      }),
      providesTags: ['ProductsPublic'],
    }),

    // ✅ Tìm kiếm sản phẩm bằng hình ảnh
    searchProductsByImage: build.mutation<any, File>({
      query: (file: File) => {
        const formData = new FormData()
        formData.append('image', file)
        return {
          url: '/products/search-by-image', // Sử dụng endpoint hiện tại
          method: 'POST',
          data: formData,
          authOptional: false, // Public endpoint, không cần token
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      },
      invalidatesTags: ['ProductsPublic'],
    }),

    // ✅ Lấy danh sách categories public
    getPublicCategories: build.query<any, any>({
      query: (params) => ({
        url: '/categories', // Sử dụng endpoint hiện tại
        method: 'GET',
        params: params,
        authOptional: false, // Public endpoint, không cần token
      }),
      providesTags: ['CategoriesPublic'],
    }),
  }),
})

export const {
  useGetPublicProductsQuery,
  useGetPublicProductDetailQuery,
  useGetPublicProductInventoryQuery,
  useGetPublicProductStatsQuery,
  useSearchProductsByImageMutation,
  useGetPublicCategoriesQuery,
} = productPublicApi
