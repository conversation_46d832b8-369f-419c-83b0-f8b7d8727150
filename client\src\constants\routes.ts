const BaseRoleRoutes = {
  R01: '/r01',
  R02: '/r02',
  R03: '/r03',
  R04: '/r04',
  R05: '/r05',
}

export const ROUTES = {
  ROOT: '/',
  ABOUT: '/gioi-thieu',
  DASHBOARD: '/bang-dieu-khien',
  LOGIN: '/dang-nhap',
  REGISTER: '/dang-ky',
  PROFILE: '/tai-khoan',
  NOT_FOUND: '/khong-tim-thay-trang',
  UNAUTHORIZE: '/truy-cap-bi-tu-choi',
  NETWORK: '/truy-cap-bi-tu-choi',
  TERMS: '/chinh-sach',
  FORGOT_PASSWORD: '/quen-mat-khau',
  OPT_RESET_PASSWORD: '/otp',
  CHANGE_PASSWORD: '/doi-mat-khau',
  COMMERCE: '/mua-sam', // Thêm route mới
  HOME: '/',
  USER_MANAGEMENT: '/quan-ly-nguoi-dung',
  PERMISSION_MANAGEMENT: '/quan-ly-quyen',

  PRODUCT_MANAGEMENT: '/quan-ly-san-pham',
  ORDER_MANAGEMENT: '/quan-ly-don-hang',
  ORDER_STATISTICS: '/thong-ke-don-hang',
  INVENTORY_MANAGEMENT: '/quan-ly-ton-kho',
  CATEGORY_MANAGEMENT: '/quan-ly-danh-muc',
  CART: '/gio-hang',
  ORDER_DETAIL: '/gio-hang/chi-tiet-don-hang',
  MY_ORDER: '/gio-hang/don-hang-cua-toi',
  PRODUCT_DETAIL: '/san-pham/:id',
  FAVORITES: '/san-pham/yeu-thich',
  CATEGORY: '/category/:id',
  SERVICES: '/services',
  NEWS: '/news',
  TRANSFER: '/transfer',
  CONTACT: '/contact',
}

export const configError = {
  NotFound: '/notfound',
  Network: '/network',
}

export const configAuthorise = {
  matcher: Object.values(BaseRoleRoutes).map((route) => `${route}/:path*`),
}
