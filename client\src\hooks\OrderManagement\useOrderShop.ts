import { useEffect } from 'react'
import {
  useGetShopOrdersQuery,
  useGetShopOrderDetailQuery,
  useUpdateOrderStatusMutation,
  useGetCancelOrderRequestsQuery,
  useAcceptCancelRequestMutation,
  type OrderManagementListParams,
  type UpdateOrderStatusRequest,
  type CancelRequestListParams,
} from '../../services/M2/OrderManagement/OrderShop/orderShop'
import { useToast } from '../../components/Toast'
import { useLoadingStore } from '@/utils/loadingStore'

// Hook lấy danh sách đơn hàng shop
const useGetShopOrdersHook = (params: OrderManagementListParams) => {
  const { data, error, isLoading, refetch, isFetching } = useGetShopOrdersQuery(params)
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook lấy chi tiết đơn hàng shop
const useGetShopOrderDetailHook = (id: string) => {
  const { data, error, isLoading, refetch, isFetching } = useGetShopOrderDetailQuery(id, {
    skip: !id,
    refetchOnMountOrArgChange: true
  })
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook lấy danh sách yêu cầu hủy đơn hàng
const useGetCancelOrderRequestsHook = (params: CancelRequestListParams) => {
  const { data, error, isLoading, refetch, isFetching } = useGetCancelOrderRequestsQuery(params)
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook cập nhật trạng thái đơn hàng
const useUpdateOrderStatusHook = () => {
  const { showSuccess, showError } = useToast()
  const [updateFunc] = useUpdateOrderStatusMutation()

  const update = async (id: string, statusId: string): Promise<boolean> => {
    try {
      const data: UpdateOrderStatusRequest = { status_id: statusId }
      const res = await updateFunc({ id, data })
      if (!res?.error) {
        showSuccess('Cập nhật trạng thái đơn hàng thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Cập nhật trạng thái thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Cập nhật trạng thái thất bại' + e)
      return false
    }
  }

  return update
}

// Hook chấp nhận yêu cầu hủy đơn hàng
const useAcceptCancelRequestHook = () => {
  const { showSuccess, showError } = useToast()
  const [acceptFunc] = useAcceptCancelRequestMutation()

  const accept = async (id: string): Promise<boolean> => {
    try {
      const res = await acceptFunc(id)
      if (!res?.error) {
        showSuccess('Chấp nhận yêu cầu hủy đơn hàng thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Chấp nhận yêu cầu hủy thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Chấp nhận yêu cầu hủy thất bại' + e)
      return false
    }
  }

  return accept
}

// Xuất ra
export {
  useGetShopOrdersHook,
  useGetShopOrderDetailHook,
  useGetCancelOrderRequestsHook,
  useUpdateOrderStatusHook,
  useAcceptCancelRequestHook,
}
