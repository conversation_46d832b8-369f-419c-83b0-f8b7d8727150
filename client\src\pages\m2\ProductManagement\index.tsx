import React, { useState, useEffect, useMemo } from 'react'
import {
  Table,
  Modal,
  Form,
  Input,
  Select,
  TreeSelect,
  Upload,
  InputNumber,
  Tag,
  Space,
  Card,
  Statistic,
  Row,
  Col,
  Checkbox,
  Image,
  Popconfirm,
  Drawer,
  Collapse,
  Descriptions,
  Divider,
  Tabs,
  Badge,
  Typography,
  Empty,
  message,
  Button,
} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  FilterOutlined,
  ExportOutlined,
  ImportOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  MinusCircleOutlined,
  InfoCircleOutlined,
  PictureOutlined,
  SettingOutlined,
  TagOutlined,
  ShoppingOutlined,
  DollarOutlined,
  InboxOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  EyeInvisibleOutlined, // <-- thêm icon ẩn sản phẩm
} from '@ant-design/icons'
import ModalButtonGroup from '@/components/ModalButtonGroup'
import ProductDetailModal from './ProductDetailModal'
import ProductCreateModal from './ProductCreateModal'
import ProductEditModal from './ProductEditModal'
import styles from './ProductManagement.module.scss'
import {
  useCreateProductHook,
  useGetCategoriesHook,
  useUploadProductImagesHook,
  useCreateProductWithImagesHook,
  useGetProductsHook,
  useUpdateProductHook,
  useUpdateProductWithImagesHook, // ✅ THÊM: Hook mới đã sửa lỗi
  useGetProductDetailHook,
  useDeleteProductHook, // <-- import hook delete
  useDisableProductHook,
  useGetStatusesByTypeHook,
  useGetPublicProductsHook, // <-- import hook disable
} from '@/hooks/product/useproduct'
import type {
  CreateProductRequest,
  ProductAttribute,
  ProductVersion,
  ProductImage,
  CategoryItem,
  ProductItem,
} from '@/services/M2/ProductManagement/productManagement'
import type { DataNode } from 'antd/es/tree'
import logo from '@/assets/images/logo.png'

const { Option } = Select
const { TextArea } = Input
const { Panel } = Collapse
const { Title, Text, Paragraph } = Typography

const ProductManagement: React.FC = () => {
  const [modalVisible, setModalVisible] = useState(false)
  const [editingProduct, setEditingProduct] = useState<ProductItem | null>(null)
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [searchText, setSearchText] = useState('')
  const [filters, setFilters] = useState({ status: '', category: '' })
  const [filterDrawerVisible, setFilterDrawerVisible] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const [detailLoading, setDetailLoading] = useState(false)
  const [form] = Form.useForm()

  // ✅ Thêm state cho phân trang
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)

  // Lấy danh sách trạng thái sản phẩm từ API
  const { data: productStatuses } = useGetStatusesByTypeHook({ type: 'PRODUCT', parent: null })
  const productStatusList = productStatuses || []

  // Hooks
  const createProductWithImages = useCreateProductWithImagesHook()
  const createProduct = useCreateProductHook()
  const updateProduct = useUpdateProductHook()
  const uploadImages = useUploadProductImagesHook()
  const deleteProduct = useDeleteProductHook() // <-- sử dụng hook delete
  const disableProduct = useDisableProductHook() // <-- sử dụng hook disable

  // Lấy danh sách sản phẩm từ API
  const {
    data: productsApiData,
    isLoading: productsLoading,
    error: productsError,
    refetch: refetchProducts,
  } = useGetProductsHook({
    page: currentPage,
    size: pageSize,
    search: searchText,
    category_id: filters.category || undefined,
    status: filters.status || undefined, // Lọc theo trạng thái nhận vào id của trạng thái
  })

  // Lấy mảng sản phẩm từ API response (chuẩn hóa)
  const products: ProductItem[] = Array.isArray(productsApiData?.data) ? productsApiData.data : []

  const loading = productsLoading

  // Lấy categories
  const {
    data: categoriesData,
    isLoading: categoriesLoading,
    error: categoriesError,
  } = useGetCategoriesHook({
    type: 'tree',
    parent_id: null,
  })

  // Convert categories to tree data
  const convertToTreeData = (categories: CategoryItem[]): DataNode[] =>
    categories
      ? categories.map((category) => ({
          title: category.name,
          value: category.id,
          key: category.id,
          children: category.children?.length ? convertToTreeData(category.children) : undefined,
        }))
      : []

  const treeCategories = useMemo((): DataNode[] => {
    let cats: CategoryItem[] = []

    if (categoriesData?.data && Array.isArray(categoriesData.data)) {
      cats = categoriesData.data
    } else if (Array.isArray(categoriesData)) {
      cats = categoriesData
    }

    return cats.length > 0 ? convertToTreeData(cats) : []
  }, [categoriesData])

  // Category mapping
  const categoryMap = useMemo(() => {
    const map: { [key: string]: string } = {}
    const extractCategories = (categories: CategoryItem[]) => {
      categories?.forEach((category) => {
        map[category.id] = category.name
        if (category.children && category.children.length > 0) {
          extractCategories(category.children)
        }
      })
    }

    let cats: CategoryItem[] = []
    if (categoriesData?.data && Array.isArray(categoriesData.data)) {
      cats = categoriesData.data
    } else if (Array.isArray(categoriesData)) {
      cats = categoriesData
    }

    if (cats.length > 0) {
      extractCategories(cats)
    }

    return map
  }, [categoriesData])

  // Error handling
  useEffect(() => {
    if (categoriesError) {
      message.error('Không thể tải danh sách danh mục')
    }
  }, [categoriesError])

  useEffect(() => {
    if (productsError) {
      message.error('Không thể tải danh sách sản phẩm')
    }
  }, [productsError])

  // ✅ Reset về trang 1 khi thay đổi search hoặc filter
  useEffect(() => {
    setCurrentPage(1)
  }, [searchText, filters.status, filters.category])

  // ✅ Debounce search để tránh gọi API quá nhiều
  useEffect(() => {
    const timer = setTimeout(() => {
      // Search sẽ được trigger bởi useGetProductsHook khi searchText thay đổi
    }, 500)

    return () => clearTimeout(timer)
  }, [searchText])

  // ✅ Xử lý trường hợp khi không có sản phẩm và currentPage > 1
  useEffect(() => {
    if (productsApiData?.total === 0 && currentPage > 1) {
      setCurrentPage(1)
    }
  }, [productsApiData?.total, currentPage])

  // ✅ Xử lý trường hợp khi currentPage vượt quá tổng số trang
  useEffect(() => {
    if (productsApiData?.total && productsApiData?.size) {
      const totalPages = Math.ceil(productsApiData.total / productsApiData.size)
      if (currentPage > totalPages && totalPages > 0) {
        setCurrentPage(totalPages)
      }
    }
  }, [productsApiData?.total, productsApiData?.size, currentPage])

  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth <= 768)
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  const statusConfig = {
    active: { color: 'green', text: 'Đang bán', icon: <CheckCircleOutlined /> },
    hidden: { color: 'orange', text: 'Đã ẩn', icon: <EyeOutlined /> },
    draft: { color: 'blue', text: 'Nháp', icon: <EditOutlined /> },
    archived: { color: 'red', text: 'Lưu trữ', icon: <InboxOutlined /> },
  }

  // Mobile columns - simplified for mobile view
  const mobileColumns = [
    {
      title: 'Sản phẩm',
      key: 'product',
      render: (_: any, record: ProductItem) => (
        <div className={styles.mobileProductCard}>
          <div className={styles.mobileProductHeader}>
            <Image
              src={
                record.thumbnail_url ||
                (record.product_images && record.product_images[0]?.image_url) ||
                logo
              }
              alt="Product"
              width={40}
              height={40}
              className={styles.productImageMobile}
              fallback={logo}
            />
            <div className={styles.mobileProductInfo}>
              <div className={styles.mobileProductName}>{record.name}</div>
              <div className={styles.mobileProductSku}>SKU: {record.slug || record.id}</div>
              <div className={styles.mobileProductPrice}>
                {Number(record.price).toLocaleString()}₫
              </div>
            </div>
          </div>
          <div className={styles.mobileProductDetails}>
            <span className={styles.mobileProductCategory}>
              {categoryMap[record.category_id] || 'Chưa phân loại'}
            </span>
            <span className={record.stock === 0 ? styles.outOfStock : styles.inStock}>
              Tồn: {record.stock}
            </span>
          </div>
        </div>
      ),
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 120,
      render: (_: any, record: ProductItem) => (
        <div className={styles.mobileActions}>
          <Button
            variant="outlined"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
            size="small"
            className={styles.mobileActionButton}
          >
            Xem
          </Button>
          <Button
            variant="outlined"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            size="small"
            className={styles.mobileActionButton}
          >
            Sửa
          </Button>
          <Button
            variant="outlined"
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
            size="small"
            danger
            className={styles.mobileActionButton}
          >
            Xóa
          </Button>
        </div>
      ),
    },
  ]

  // Desktop columns
  const desktopColumns = [
    {
      title: 'Ảnh',
      dataIndex: 'thumbnail_url',
      key: 'thumbnail_url',
      width: 80,
      render: function renderImage(_: any, record: ProductItem) {
        const imgUrl =
          record.thumbnail_url ||
          ((record as any).product_images && (record as any).product_images[0]?.image_url) ||
          '/images/no-image.png'
        return (
          <Image
            src={
              record.thumbnail_url ||
              (record.product_images && record.product_images[0]?.image_url) ||
              logo
            }
            alt="Product"
            width={50}
            height={50}
            className={styles.productImage}
            fallback={logo}
          />
        )
      },
    },
    {
      title: 'Tên sản phẩm',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => <div className={styles.productName}>{text}</div>,
    },
    {
      title: 'SKU',
      dataIndex: 'sku',
      key: 'sku',
      render: (_: any, record: ProductItem) => (
        <code className={styles.sku}>{record.slug || record.id}</code>
      ),
    },
    {
      title: 'Danh mục',
      dataIndex: 'category',
      key: 'category',
      render: (_: any, record: ProductItem) => categoryMap[record.category_id] || '',
    },
    {
      title: 'Giá bán',
      dataIndex: 'price',
      key: 'price',
      render: (price: any) => <span>{Number(price).toLocaleString()}₫</span>,
    },
    {
      title: 'Đơn vị',
      dataIndex: 'unit',
      key: 'unit',
    },
    {
      title: 'Tồn kho',
      dataIndex: 'stock',
      key: 'stock',
      render: (stock: number) => (
        <span className={stock === 0 ? styles.outOfStock : styles.inStock}>{stock}</span>
      ),
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (_: any, record: ProductItem) => {
        // Ưu tiên lấy status_label và status_label_color từ backend, fallback về status string
        if (record.status_label) {
          return <Tag color={record.status_label_color || 'default'}>{record.status_label}</Tag>
        }
        // Nếu status là object (có label)
        if (
          typeof record.status === 'object' &&
          record.status !== null &&
          'label' in record.status
        ) {
          return <Tag color={record.status.color || 'default'}>{record.status.label}</Tag>
        }
        // Nếu status là string
        const statusKey =
          typeof record.status === 'string' &&
          ['active', 'hidden', 'draft', 'archived'].includes(record.status)
            ? (record.status as 'active' | 'hidden' | 'draft' | 'archived')
            : 'active'
        const config = statusConfig[statusKey]
        return (
          <Tag color={config?.color}>
            {config?.text || (typeof record.status === 'string' ? record.status : '')}
          </Tag>
        )
      },
    },
    {
      title: 'Hành động',
      key: 'action',
      width: 180,
      render: (_: any, record: ProductItem) => (
        <Space size="small">
          <Button
            variant="outlined"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            size="small"
          >
            Sửa
          </Button>
          <Button
            variant="outlined"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
            size="small"
          >
            Xem
          </Button>
          <Button
            variant="outlined"
            icon={<EyeInvisibleOutlined />}
            onClick={() => handleDisable(record.id)}
            size="small"
          >
            Ẩn
          </Button>
          <Popconfirm title="Bạn có chắc chắn muốn xóa?" onConfirm={() => handleDelete(record.id)}>
            <Button variant="outlined" icon={<DeleteOutlined />} size="small">
              Xóa
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  // State cho modal xem chi tiết
  const [viewModalVisible, setViewModalVisible] = useState(false)
  const [viewProductId, setViewProductId] = useState<string | null>(null)
  const [editProductId, setEditProductId] = useState<string | null>(null)
  const { data: viewProductData, isLoading: viewLoading } = useGetProductDetailHook(
    viewProductId || ''
  )
  const { data: editProductData, isLoading: editLoading } = useGetProductDetailHook(
    editProductId || ''
  )

  // Khi nhấn xem chi tiết
  const handleView = (product: ProductItem) => {
    setViewProductId(product.id)
    setViewModalVisible(true)
  }

  // Khi nhấn chỉnh sửa
  const handleEdit = (product: ProductItem) => {
    setEditProductId(product.id)
    setModalVisible(true)
  }

  // Nút ẩn sản phẩm
  const handleDisable = async (id: string) => {
    const ok = await disableProduct(id)
    if (ok) {
      // ✅ Reset về trang 1 sau khi ẩn sản phẩm
      setCurrentPage(1)
      refetchProducts()
    }
  }

  // Khi modal xem chi tiết đóng
  useEffect(() => {
    if (!viewModalVisible) setViewProductId(null)
  }, [viewModalVisible])

  // Khi modal edit đóng
  useEffect(() => {
    if (!modalVisible) setEditProductId(null)
  }, [modalVisible])

  // Khi có data editProductData thì set vào form
  useEffect(() => {
    if (editProductId && editProductData) {
      setEditingProduct(editProductData)
      const fixedAttributes = (editProductData.product_attribute || []).map((attr: any) => ({
        ...attr,
        attribute_name: attr.attribute_name || attr.name || '',
      }))
      // Determine the correct status id for the select
      let statusId = ''
      if (editProductData.status_id) {
        statusId = editProductData.status_id
      } else if (typeof editProductData.status === 'object' && editProductData.status?.id) {
        statusId = editProductData.status.id
      } else if (typeof editProductData.status === 'string') {
        statusId = editProductData.status
      }
      form.setFieldsValue({
        ...editProductData,
        status: statusId,
        product_attribute: fixedAttributes,
        product_versions: editProductData.product_versions || [],
        product_images: editProductData.product_images || [],
      })
    }
  }, [editProductId, editProductData])

  const handleDelete = async (id: string) => {
    const ok = await deleteProduct(id)
    if (ok) {
      // ✅ Reset về trang 1 sau khi xóa để tránh trang trống
      setCurrentPage(1)
      refetchProducts()
    }
  }

  const handleAdd = () => {
    setEditingProduct(null)
    form.resetFields()
    setModalVisible(true)
  }

  // ...existing code...

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
  }

  // ...existing code...

  return (
    <Card>
      <div className={styles.container}>
        {/* Header */}
        <div className={styles.header}>
          <div className={styles.headerTitle}>
            <h1>Quản lý sản phẩm</h1>
            {!isMobile && <p>Quản lý toàn bộ sản phẩm của cửa hàng</p>}
          </div>
          <div className={styles.headerActions}>
            <Button
              variant="outlined"
              icon={<PlusOutlined />}
              onClick={handleAdd}
              className={styles.addBtn}
            >
              {isMobile ? 'Thêm' : 'Thêm sản phẩm'}
            </Button>
          </div>
        </div>

        {/* Search Bar - Mobile */}
        {isMobile && (
          <div className={styles.mobileSearchContainer}>
            <Input
              placeholder="Tìm kiếm sản phẩm..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              className={styles.mobileSearchInput}
            />
          </div>
        )}

        {/* Stats */}
        <Row gutter={16} className={styles.statsRow}>
          <Col xs={24} sm={12} lg={6}>
            <Card className={styles.statCard}>
              <Statistic
                title="Tổng sản phẩm"
                value={products.length}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card className={styles.statCard}>
              <Statistic
                title="Đang bán"
                value={products.filter((p) => p.status === 'active').length}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card className={styles.statCard}>
              <Statistic
                title="Nháp"
                value={products.filter((p) => p.status === 'draft').length}
                valueStyle={{ color: '#ff4d4f' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card className={styles.statCard}>
              <Statistic
                title="Lưu trữ"
                value={products.filter((p) => p.status === 'archived').length}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
        </Row>

        {/* Filters */}
        {!isMobile && (
          <Card className={styles.filterCard}>
            <Row gutter={16}>
              <Col xs={24} sm={12} md={8} lg={6}>
                <div style={{ marginBottom: 4, fontWeight: 500 }}>Tìm kiếm</div>
                <Input
                  placeholder="Tìm kiếm theo tên, SKU..."
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  className={styles.searchInput}
                />
              </Col>
              <Col xs={24} sm={12} md={8} lg={6}>
                <div style={{ marginBottom: 4, fontWeight: 500 }}>Trạng thái</div>
                <Select
                  placeholder="Trạng thái"
                  value={filters.status}
                  onChange={(value) => setFilters({ ...filters, status: value })}
                  className={styles.filterSelect}
                  allowClear
                  loading={!productStatuses}
                >
                  {Array.isArray(productStatuses) &&
                    productStatuses.map((status) => (
                      <Option key={status.id} value={status.id}>
                        {status.label}
                      </Option>
                    ))}
                </Select>
              </Col>
              <Col xs={24} sm={12} md={8} lg={6}>
                <div style={{ marginBottom: 4, fontWeight: 500 }}>Danh mục</div>
                <TreeSelect
                  placeholder="Danh mục"
                  value={filters.category}
                  onChange={(value) => setFilters({ ...filters, category: value })}
                  className={styles.filterSelect}
                  allowClear
                  loading={categoriesLoading}
                  treeData={treeCategories}
                  treeDefaultExpandAll={false}
                  showSearch
                  treeNodeFilterProp="title"
                  notFoundContent={categoriesLoading ? 'Đang tải...' : 'Không có danh mục'}
                  style={{ width: '100%' }}
                />
              </Col>
            </Row>
          </Card>
        )}

        {/* Bulk Actions */}
        {selectedRowKeys.length > 0 && (
          <Card className={styles.bulkCard}>
            <div className={styles.bulkActions}>
              <span>Đã chọn {selectedRowKeys.length} sản phẩm</span>
              <Space>
                <Button onClick={() => setSelectedRowKeys([])}>Bỏ chọn</Button>
                <Button type="primary" danger>
                  Xóa đã chọn
                </Button>
              </Space>
            </div>
          </Card>
        )}

        {/* Products Table */}
        <Card className={styles.tableCard}>
          <Table
            columns={isMobile ? mobileColumns : desktopColumns}
            dataSource={products}
            rowKey="id"
            loading={loading}
            rowSelection={isMobile ? undefined : rowSelection}
            locale={{
              emptyText: (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description={
                    <div>
                      <p>Không có sản phẩm nào</p>
                      {searchText || filters.status || filters.category ? (
                        <p>Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm</p>
                      ) : (
                        <Button type="primary" onClick={handleAdd}>
                          Thêm sản phẩm đầu tiên
                        </Button>
                      )}
                    </div>
                  }
                />
              ),
            }}
            pagination={{
              total: productsApiData?.total || 0,
              current: currentPage,
              pageSize: pageSize,
              showSizeChanger: !isMobile,
              showQuickJumper: !isMobile,
              showTotal: (total, range) =>
                isMobile
                  ? `${range[0]}-${range[1]}/${total}`
                  : `${range[0]}-${range[1]} của ${total} sản phẩm`,
              size: isMobile ? 'small' : 'default',
              onChange: (page, pageSize) => {
                // ✅ Cập nhật state phân trang và gọi lại API
                setCurrentPage(page)
                setPageSize(pageSize)
              },
              onShowSizeChange: (current, size) => {
                // ✅ Xử lý khi thay đổi pageSize, reset về trang 1
                setCurrentPage(1)
                setPageSize(size)
              },
              pageSizeOptions: ['10', '20', '50', '100'],
              disabled: loading,
            }}
            className={styles.productTable}
            scroll={isMobile ? { x: 'max-content' } : undefined}
          />
        </Card>

        {/* Add/Edit Modal - tách riêng */}
        {editingProduct ? (
          <ProductEditModal
            open={modalVisible}
            onCancel={() => setModalVisible(false)}
            form={form}
            treeCategories={treeCategories}
            categoriesLoading={categoriesLoading}
            productStatuses={productStatusList}
            editingProduct={editingProduct}
            handleDisable={handleDisable}
          />
        ) : (
          <ProductCreateModal
            open={modalVisible}
            onCancel={() => setModalVisible(false)}
            form={form}
            treeCategories={treeCategories}
            categoriesLoading={categoriesLoading}
          />
        )}

        {/* Modal xem chi tiết sản phẩm - tách riêng */}
        <ProductDetailModal
          open={viewModalVisible}
          onCancel={() => setViewModalVisible(false)}
          onEdit={() => {
            setViewModalVisible(false)
            if (viewProductData) {
              handleEdit(viewProductData)
            }
          }}
          loading={viewLoading}
          product={viewProductData}
          statusConfig={statusConfig}
          productStatusList={productStatusList}
          categoryMap={categoryMap}
          isMobile={isMobile}
        />
      </div>
    </Card>
  )
}

export default ProductManagement
