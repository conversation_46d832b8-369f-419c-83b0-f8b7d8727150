import React, { useState, useEffect, useRef } from 'react'
import { useGetProductsHook } from '@/hooks/product/useproduct'
import { useCategoryTree } from '@/hooks/product/useCategory'
import { motion, AnimatePresence } from 'framer-motion'
import { Pagination } from 'antd'
import {
  FiChevronDown,
  FiChevronUp,
  FiX,
  FiMenu,
  FiArrowLeft,
  FiArrowRight,
  FiHeart,
  FiShoppingCart,
} from 'react-icons/fi'

import styles from './Commerce.module.scss'

// Import assets
import marketIcon from '../../assets/images/logo.png'
import livestockIcon from '../../assets/images/logo.png'
import beautyIcon from '../../assets/images/logo.png'
import foodIcon from '../../assets/images/logo.png'
import agricultureIcon from '../../assets/images/logo.png'
import healthIcon from '../../assets/images/logo.png'
import ruralIcon from '../../assets/images/logo.png'
import educationIcon from '../../assets/images/logo.png'
import brand1 from '../../assets/images/logo.png'
import brand2 from '../../assets/images/logo.png'
import brand3 from '../../assets/images/logo.png'
import brand4 from '../../assets/images/logo.png'
import { useAddToCartHook, useGetCartHook } from '@/hooks/cart/useCart'
import ResponsiveImage from '../../components/ResponsiveImage';
import {
  useGetFavoritesHook,
  useToggleFavoriteHook,
} from '@/hooks/product/useproduct'
import { useRef as useReactRef } from 'react'
import { ROUTES } from '@/constants/routes'
import { useNavigate, useLocation, useSearchParams } from 'react-router-dom'
import { useSelector } from 'react-redux'

interface Product {
  id: string
  name: string
  price: number
  image: string
  seller: string
  rating: number
  reviews: number
  category: string
  isFavorite?: boolean
  origin?: string
  status?: string
  brand?: string
  product_versions?: { id: string; price: number; stock: number }[] // Thêm type cho versions
}

interface Category {
  id: string
  name: string
  icon: string
  count: number
  description?: string
  children?: Category[]
}

interface Brand {
  id: string
  name: string
  logo: string
}

interface Tab {
  id: number
  name: string
  active: boolean
}

const Commerce: React.FC = () => {
  // Hooks for navigation and URL params
  const navigate = useNavigate()
  const location = useLocation()
  const [searchParams] = useSearchParams()

  // Nhận kết quả tìm kiếm bằng ảnh từ Header
  const [imageSearchResult, setImageSearchResult] = useState<any>(null)
  const [autoSwitchedToImageTab, setAutoSwitchedToImageTab] = useState(false)

  const handleImageSearchResult = (result: any) => {
    // Luôn cập nhật kết quả mới và chuyển tab
    setImageSearchResult(result)

    // Tự động chuyển sang tab "Tìm kiếm bằng ảnh" sau khi tìm kiếm
    // Bất kể có kết quả hay không để người dùng tiện xem
    setActiveTab(6)
    setAutoSwitchedToImageTab(true)

    // Reset flag sau 2 giây
    setTimeout(() => {
      setAutoSwitchedToImageTab(false)
    }, 2000)

    // Scroll mượt đến phần sản phẩm sau khi chuyển tab
    setTimeout(() => {
      const productsSection = document.querySelector(`.${styles.productsSection}`)
      if (productsSection) {
        productsSection.scrollIntoView({ behavior: 'smooth', block: 'start' })
      }
    }, 100)
  }
  const [searchQuery, setSearchQuery] = useState<string>('')
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState<string>('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [debouncedSelectedCategory, setDebouncedSelectedCategory] = useState<string>('all')
  const { data: categoriesData, isLoading: categoriesLoading, error: categoriesError } = useCategoryTree({ type: 'tree' })

  // Favorites hooks
  const auth = useSelector((state: any) => state.auth)
  // Chỉ gọi các hook favorites nếu đã đăng nhập
  const { data: favoritesData, refetch: refetchFavorites } = auth?.id
    ? useGetFavoritesHook()
    : { data: [], refetch: () => {} }
  const toggleFavorite = auth?.id ? useToggleFavoriteHook() : () => Promise.resolve(null)
  // Tạo danh mục mẫu nếu không có dữ liệu từ API - sử dụng UUID thực tế
  const sampleCategories: Category[] = [
    {
      id: 'e2b8bc36-8b1b-4aee-94a5-ad3979577e9f',
      name: 'Văn phòng phẩm & Sách',
      icon: marketIcon,
      count: 2,
      children: [
        { id: 'e2b8bc36-8b1b-4aee-94a5-ad3979577e9f-1', name: 'Văn phòng phẩm', icon: marketIcon, count: 0 },
        { id: 'e2b8bc36-8b1b-4aee-94a5-ad3979577e9f-2', name: 'Sách', icon: marketIcon, count: 0 },
      ],
    },
    {
      id: '3559d661-9003-4669-bdab-bf1d15cc1529',
      name: 'Thời trang',
      icon: marketIcon,
      count: 2,
      children: [
        { id: '3559d661-9003-4669-bdab-bf1d15cc1529-1', name: 'Áo quần', icon: marketIcon, count: 0 },
        { id: '3559d661-9003-4669-bdab-bf1d15cc1529-2', name: 'Giày dép', icon: marketIcon, count: 0 },
      ],
    },
    {
      id: '78de1119-a3fa-4afe-bef6-4b00a469cc5d',
      name: 'Đồ gia dụng',
      icon: marketIcon,
      count: 1,
      children: [{ id: '78de1119-a3fa-4afe-bef6-4b00a469cc5d-1', name: 'Nhà bếp', icon: marketIcon, count: 0 }],
    },
    { id: '4', name: 'Giao hàng', icon: marketIcon, count: 0 },
    { id: '5', name: 'Gọi xe', icon: marketIcon, count: 0 },
    { id: '6', name: 'Du lịch', icon: marketIcon, count: 0 },
    { id: '7', name: 'Đào tạo', icon: marketIcon, count: 0 },
    { id: '8', name: 'Việc làm', icon: marketIcon, count: 0 },
    { id: '9', name: 'Y tế', icon: marketIcon, count: 0 },
    { id: '10', name: 'Mạng xã hội', icon: marketIcon, count: 0 },
    { id: '11', name: 'Nông nghiệp', icon: marketIcon, count: 0 },
    { id: '12', name: 'Bảo hiểm', icon: marketIcon, count: 0 },
  ]

  // Tạm thời sử dụng categories từ API hoặc sample categories
  const initialCategories: Category[] =
    Array.isArray(categoriesData) && categoriesData.length > 0 && !categoriesError
      ? categoriesData.map((cat: any) => ({
          id: cat.id,
          name: cat.name,
          icon: cat.icon_url || marketIcon,
          count: Array.isArray(cat.children) ? cat.children.length : 0,
          description: cat.description || '',
          children: Array.isArray(cat.children)
            ? cat.children.map((child: any) => ({
                id: child.id,
                name: child.name,
                icon: child.icon_url || marketIcon,
                count: 0,
                description: child.description || '',
              }))
            : undefined,
        }))
      : sampleCategories



  // Dữ liệu thương hiệu đối tác ảo
  const brands: Brand[] = [
    { id: '1', name: 'Samsung', logo: marketIcon },
    { id: '2', name: 'Apple', logo: marketIcon },
    { id: '3', name: 'Dell', logo: marketIcon },
    { id: '4', name: 'HP', logo: marketIcon },
    { id: '5', name: 'Lenovo', logo: marketIcon },
    { id: '6', name: 'Asus', logo: marketIcon },
    { id: '7', name: 'Acer', logo: marketIcon },
    { id: '8', name: 'MSI', logo: marketIcon },
    { id: '9', name: 'Nike', logo: marketIcon },
    { id: '10', name: 'Adidas', logo: marketIcon },
    { id: '11', name: 'Puma', logo: marketIcon },
    { id: '12', name: 'Under Armour', logo: marketIcon },
    { id: '13', name: 'Bosch', logo: marketIcon },
    { id: '14', name: 'Philips', logo: marketIcon },
    { id: '15', name: 'Panasonic', logo: marketIcon },
    { id: '16', name: 'LG', logo: marketIcon },
    { id: '17', name: 'Sony', logo: marketIcon },
    { id: '18', name: 'Canon', logo: marketIcon },
    { id: '19', name: 'Nikon', logo: marketIcon },
    { id: '20', name: 'GoPro', logo: marketIcon },
  ]
  const [cartItemCount, setCartItemCount] = useState<number>(0)
  const [activeTab, setActiveTab] = useState<number>(1)
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)
  const [selectedBrands, setSelectedBrands] = useState<string[]>([])
  const [selectedPriceRange, setSelectedPriceRange] = useState<string>('all')
  const [debouncedSelectedBrands, setDebouncedSelectedBrands] = useState<string[]>([])
  const [debouncedSelectedPriceRange, setDebouncedSelectedPriceRange] = useState<string>('all')
  const [openCategoryIds, setOpenCategoryIds] = useState<string[]>([])

  // State cho pagination
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [pageSize, setPageSize] = useState<number>(20)

  const brandsGridRef = useRef<HTMLDivElement>(null)

  const toggleCategory = (id: string) => {
    setOpenCategoryIds((prev) => {
      const newIds = prev.includes(id) ? prev.filter((cid) => cid !== id) : [...prev, id]
      return newIds
    })
  }

  const tabs: Tab[] = [
    { id: 1, name: 'Tất cả sản phẩm', active: true },
    { id: 2, name: 'Hàng mới', active: false },
    { id: 3, name: 'Đang giảm giá', active: false },
    { id: 4, name: 'Xếp hạng cao nhất', active: false },
    { id: 5, name: 'Xu hướng', active: false },
    { id: 6, name: 'Tìm kiếm bằng ảnh', active: false },
  ]

  // Helper function để scroll đến phần tabs
  const scrollToTabs = (delay = 100) => {
    setTimeout(() => {
      const tabsSection = document.querySelector(`.${styles.styleTabsContainer}`)
      if (tabsSection) {
        tabsSection.scrollIntoView({ behavior: 'smooth', block: 'start' })
      }
    }, delay)
  }

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    setCurrentPage(1) // Reset về trang 1 khi search
  }

  // Helper function để check xem sản phẩm có đang bán không
  const isProductOnSale = (product: any): boolean => {
    const activeStatusValues = [
      'active', 'ACTIVE', 'Active',
      'Đang bán', 'đang bán', 'ĐANG BÁN',
      'on_sale', 'ON_SALE', 'available', 'AVAILABLE'
    ];

    const inactiveStatusValues = [
      'inactive', 'INACTIVE', 'Inactive',
      'Đã ẩn', 'đã ẩn', 'ĐÃ ẨN',
      'Hết hàng', 'hết hàng', 'HẾT HÀNG',
      'Ngừng bán', 'ngừng bán', 'NGỪNG BÁN',
      'draft', 'DRAFT', 'Draft',
      'disabled', 'DISABLED', 'Disabled'
    ];

    if (typeof product.status === 'string') {
      if (inactiveStatusValues.includes(product.status)) {
        return false;
      }
      return activeStatusValues.includes(product.status);
    }

    if (typeof product.status === 'object' && product.status !== null) {
      const statusCode = product.status.code;
      const statusLabel = product.status.label;
      const statusName = product.status.name;

      if (inactiveStatusValues.includes(statusCode) ||
          inactiveStatusValues.includes(statusLabel) ||
          inactiveStatusValues.includes(statusName)) {
        return false;
      }

      return activeStatusValues.includes(statusCode) ||
             activeStatusValues.includes(statusLabel) ||
             activeStatusValues.includes(statusName);
    }

    if (product.status_label) {
      if (inactiveStatusValues.includes(product.status_label)) {
        return false;
      }
      return activeStatusValues.includes(product.status_label);
    }

    return false;
  };



  // Helper function để convert price range thành min/max values
  const getPriceRange = (range: string): { min?: number; max?: number } => {
    switch (range) {
      case 'very-low':
        return { max: 50000 }
      case 'low':
        return { min: 50000, max: 100000 }
      case 'medium':
        return { min: 100000, max: 300000 }
      case 'medium-high':
        return { min: 300000, max: 500000 }
      case 'high':
        return { min: 500000, max: 1000000 }
      case 'very-high':
        return { min: 1000000 }
      default:
        return {}
    }
  }

  const priceRange = getPriceRange(debouncedSelectedPriceRange)

  // Hook để lấy products - phải đặt trước useEffect để tránh lỗi hoisting
  const {
    data: productsData,
    isLoading: productsLoading,
    error: productsError,
    refetch: refetchProducts,
  } = useGetProductsHook({
    page: currentPage,
    size: pageSize,
    search: debouncedSearchQuery,
    category_id: debouncedSelectedCategory !== 'all' ? debouncedSelectedCategory : undefined,
    price_min: priceRange.min,
    price_max: priceRange.max,
    // Note: Backend không support brand filtering, sử dụng client-side filtering cho brands
  })

  // Sử dụng categories từ API (đã hoạt động cho cả guest users)
  const categories: Category[] = initialCategories

  // Effect để đọc search query và image search từ URL params khi component mount
  useEffect(() => {
    const searchFromUrl = searchParams.get('search')
    const imageSearchFromUrl = searchParams.get('imageSearch')

    if (searchFromUrl) {
      // Set search query ngay lập tức để trigger API call
      setSearchQuery(searchFromUrl)
      setDebouncedSearchQuery(searchFromUrl)

      // Chuyển về tab "Tất cả sản phẩm" (tab 1) để hiển thị kết quả
      setActiveTab(1)

      // Reset category filter để hiển thị tất cả kết quả
      setSelectedCategory('all')

      // Force refetch để đảm bảo API được gọi với search query mới
      setTimeout(() => {
        refetchProducts()
      }, 100)

      // Scroll đến phần tabs sau khi component đã render
      scrollToTabs(800) // Tăng delay để đảm bảo data đã load

      // Clear URL params sau khi đã xử lý
      navigate(location.pathname, { replace: true })
    }

    if (imageSearchFromUrl === 'true') {
      // Đọc image search result từ sessionStorage
      const storedResult = sessionStorage.getItem('imageSearchResult')
      if (storedResult) {
        try {
          const result = JSON.parse(storedResult)
          handleImageSearchResult(result)
          // Clear sessionStorage sau khi đã xử lý
          sessionStorage.removeItem('imageSearchResult')
        } catch (error) {
          console.error('Error parsing image search result:', error)
        }
      }
      // Clear URL params sau khi đã xử lý
      navigate(location.pathname, { replace: true })
    }
  }, [searchParams, navigate, location.pathname, refetchProducts])

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery)
    }, 300) // 300ms delay

    return () => clearTimeout(timeoutId)
  }, [searchQuery])

  // Debounced category filter effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebouncedSelectedCategory(selectedCategory)
    }, 300) // 300ms delay

    return () => clearTimeout(timeoutId)
  }, [selectedCategory])

  // Debounced brands filter effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebouncedSelectedBrands(selectedBrands)
    }, 300) // 300ms delay

    return () => clearTimeout(timeoutId)
  }, [selectedBrands])

  // Debounced price range filter effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebouncedSelectedPriceRange(selectedPriceRange)
    }, 300) // 300ms delay

    return () => clearTimeout(timeoutId)
  }, [selectedPriceRange])

  // Reset về trang 1 khi filter thay đổi
  useEffect(() => {
    setCurrentPage(1)
  }, [debouncedSearchQuery, debouncedSelectedCategory, debouncedSelectedBrands, debouncedSelectedPriceRange])

  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategory(categoryId)
    setCurrentPage(1) // Reset về trang 1 khi thay đổi category
  }

  const handleTabClick = (tabId: number) => {
    setActiveTab(tabId)
    setCurrentPage(1) // Reset về trang 1 khi thay đổi tab
  }

  const handleCategoryNavigation = (categoryId: string) => {
    navigate(ROUTES.CATEGORY.replace(':id', categoryId))
  }

  const handleFilterCategoryChange = (category: string) => {
    // Thay đổi logic để chỉ cho phép chọn một category tại một thời điểm
    // Nếu category đã được chọn, bỏ chọn (set về 'all')
    // Nếu chưa được chọn, chọn category đó
    if (selectedCategory === category) {
      setSelectedCategory('all')
    } else {
      setSelectedCategory(category)
    }
  }

  const handleBrandChange = (brand: string) => {
    setSelectedBrands(
      selectedBrands.includes(brand)
        ? selectedBrands.filter((b) => b !== brand)
        : [...selectedBrands, brand]
    )
    setCurrentPage(1) // Reset về trang 1 khi thay đổi brand
  }

  const handlePriceRangeChange = (range: string) => {
    setSelectedPriceRange(range)
    setCurrentPage(1) // Reset về trang 1 khi thay đổi price range
  }

  const handleClearFilters = () => {
    setSelectedCategory('all')
    setSelectedBrands([])
    setSelectedPriceRange('all')
    setCurrentPage(1) // Reset về trang 1 khi xóa filter
  }

  const scrollLeft = () => {
    if (brandsGridRef.current) {
      brandsGridRef.current.scrollLeft -= 300
    }
  }

  const scrollRight = () => {
    if (brandsGridRef.current) {
      brandsGridRef.current.scrollLeft += 300
    }
  }



  const products = Array.isArray(productsData?.data)
    ? productsData.data.map((item: any) => {


        // Kiểm tra xem sản phẩm có trong danh sách yêu thích không
        const isFavorite =
          Array.isArray(favoritesData) && favoritesData.some((fav: any) => fav.id === item.id)

        return {
          id: item.id,
          name: item.name,
          price: Number(item.price),
          image: item.thumbnail_url || marketIcon,
          seller: item.source || 'N/A',
          rating: 5,
          reviews: 0,
          category: item.category_id,
          isFavorite: isFavorite,
          origin: '',
          status: item.status,
          status_label: item.status_label,
          brand: '',
          product_versions: item.product_versions || [],
        }
      })
    : []


  // Lấy dữ liệu sản phẩm từ kết quả tìm kiếm ảnh
  let imageSearchProducts =
    imageSearchResult?.data?.products?.data || imageSearchResult?.products?.data || []

  // Nếu imageSearchProducts là object có property data, lấy data đó
  if (
    imageSearchProducts &&
    typeof imageSearchProducts === 'object' &&
    !Array.isArray(imageSearchProducts) &&
    imageSearchProducts.data
  ) {
    imageSearchProducts = imageSearchProducts.data
  }

  // Logic hiển thị sản phẩm theo tab
  let filteredProducts: any[] = []

  if (activeTab === 6) {
    // Tab "Tìm kiếm bằng ảnh" - chỉ hiển thị khi có kết quả tìm kiếm ảnh

    // Chỉ hiển thị kết quả tìm kiếm ảnh khi có dữ liệu
    if (imageSearchResult && Array.isArray(imageSearchProducts) && imageSearchProducts.length > 0) {
      filteredProducts = imageSearchProducts.map((item: any) => {
        // Tính tổng stock từ inventory hoặc fallback về product.stock
        let totalStock = 0
        if (item.product_versions && item.product_versions.length > 0) {
          totalStock = item.product_versions.reduce(
            (sum: number, v: any) => sum + (Number(v.stock) || 0),
            0
          )
        } else {
          totalStock = Number(item.stock) || 0
        }
        // Kiểm tra xem sản phẩm có trong danh sách yêu thích không
        const isFavorite =
          Array.isArray(favoritesData) && favoritesData.some((fav: any) => fav.id === item.id)
        return {
          id: item.id,
          name: item.name,
          price: Number(item.price),
          image: item.thumbnail_url || marketIcon,
          seller: item.source || 'N/A',
          rating: 5,
          reviews: 0,
          category: item.category_id,
          isFavorite: isFavorite,
          origin: '',
          status: item.status,
          status_label: item.status_label,
          brand: '',
          product_versions: item.product_versions || [],
        }
      })
    }
    // Nếu không có kết quả tìm kiếm ảnh, không hiển thị sản phẩm nào
  } else {
    // Các tab khác - lọc sản phẩm theo logic thông thường
    // Lọc chỉ sản phẩm đang bán
    const productsOnSale = products.filter((product) => {
      return isProductOnSale(product);
    })



    filteredProducts = productsOnSale.filter((product) => {
      // Chỉ sử dụng client-side filtering cho brands vì API chưa hỗ trợ
      // Search, category và price range đã được xử lý ở API level
      const brandMatch = debouncedSelectedBrands.length === 0 || debouncedSelectedBrands.includes(product.brand || '')

      let tabMatch = true
      if (activeTab === 1) {
        // Tab "Tất cả sản phẩm" - hiển thị tất cả
        tabMatch = true
      } else if (activeTab === 2) {
        tabMatch = parseInt(product.id) <= 4
      } else if (activeTab === 3) {
        tabMatch = product.price <= 150000
      } else if (activeTab === 4) {
        tabMatch = ['suc-khoe', 'dan-muc-san'].includes(product.category)
      } else if (activeTab === 5) {
        tabMatch = parseInt(product.id) >= 5
      }

      return brandMatch && tabMatch
    })


  }

  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price)
  }

  // Xử lý toggle favorite
  const handleToggleFavorite = async (productId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    if (!auth?.id) {
      navigate(ROUTES.LOGIN, { state: { from: window.location.pathname } })
      return
    }
    try {
      const result = await toggleFavorite(productId)
      if (result !== null) {
        refetchFavorites()
      }
    } catch (error) {
      console.error('Error toggling favorite:', error)
    }
  }

  // Xử lý thêm vào giỏ hàng
  const handleAddToCart = async (product: any, e: React.MouseEvent) => {
    e.stopPropagation()

    // Kiểm tra permission và function
    if (!hasCartPermission || !addToCart) {
      return
    }

    // Kiểm tra product_versions
    if (!product.product_versions || !Array.isArray(product.product_versions) || product.product_versions.length === 0) {
      return
    }

    const defaultVersion = product.product_versions[0]
    if (!defaultVersion || !defaultVersion.id) {
      return
    }

    // Lấy giá từ version hoặc fallback về giá sản phẩm
    const unitPrice = defaultVersion.price || product.price || 0

    try {
      const success = await addToCart({
        product_id: product.id,
        version_id: defaultVersion.id,
        quantity: 1,
        unit_price: unitPrice,
        selected_options: {},
      })

      if (success) {
        // Cart sẽ tự động refetch nhờ RTK Query invalidatesTags
      }
    } catch (error) {
      console.error('Error adding to cart:', error)
    }
  }

  // Sử dụng hook mới - chỉ khi đã đăng nhập
  const addToCart = auth?.id ? useAddToCartHook() : () => Promise.resolve(null)
  const { data: cartData } = auth?.id
    ? useGetCartHook()
    : { data: null }
  // Đơn giản hóa logic permissions - tất cả user đã đăng nhập đều có thể sử dụng cart và favorite
  const hasFavoritePermission = !!auth?.id // Chỉ cần đăng nhập
  const hasCartPermission = !!auth?.id // Chỉ cần đăng nhập
  const hasOrderPermission = !!auth?.id // Chỉ cần đăng nhập

  // Cập nhật cart count từ API khi cần
  useEffect(() => {
    if (cartData) {
      const cartItems = (Array.isArray(cartData) ? cartData : cartData.data) || []
      setCartItemCount(cartItems.length)
    }
  }, [cartData])

  if (productsLoading) {
    return (
      <div className={styles.commercePage}>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Đang tải sản phẩm...</p>
        </div>
      </div>
    )
  }
  if (productsError) {
    return (
      <div className={styles.commercePage}>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Không thể tải sản phẩm. Vui lòng thử lại sau.</p>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.commercePage}>

      {/* Filter Sidebar */}
      <AnimatePresence>
        {isSidebarOpen && (
          <motion.aside
            className={styles.sidebar}
            initial={{ x: '-100%' }}
            animate={{ x: 0 }}
            exit={{ x: '-100%' }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
          >
            <div className={styles.sidebarHeader}>
              <h2>Bộ lọc sản phẩm</h2>
              <button className={styles.sidebarClose} onClick={() => setIsSidebarOpen(false)}>
                <FiX />
              </button>
            </div>
            <div className={styles.filterSection}>
              <h3 className={styles.filterTitle}>Danh mục</h3>
              {/* Option "Tất cả" */}
              <div className={styles.categoryFilterItem}>
                <div className={styles.categoryHeader}>
                  <input
                    type="radio"
                    name="categoryFilter"
                    id="category-all"
                    checked={selectedCategory === 'all'}
                    onChange={() => handleFilterCategoryChange('all')}
                  />
                  <label htmlFor="category-all">Tất cả danh mục</label>
                </div>
              </div>
              {categories
                .filter((cat) => cat.id !== 'all')
                .map((category) => (
                  <div key={category.id} className={styles.categoryFilterItem}>
                    <div
                      className={styles.categoryHeader}
                      onClick={() => toggleCategory(category.id)}
                    >
                      <input
                        type="radio"
                        name="categoryFilter"
                        id={`category-${category.id}`}
                        checked={selectedCategory === category.id}
                        onChange={(e) => {
                          e.stopPropagation()
                          handleFilterCategoryChange(category.id)
                        }}
                      />
                      <label htmlFor={`category-${category.id}`}>{category.name}</label>
                      <span className={styles.categoryCount}>({category.count})</span>
                      <FiChevronDown
                        className={`${styles.categoryArrow} ${openCategoryIds.includes(category.id) ? styles.rotated : ''}`}
                      />
                    </div>
                    {openCategoryIds.includes(category.id) &&
                      category.children &&
                      category.children.length > 0 && (
                        <div className={styles.subCategories}>
                          {category.children.map((subCategory: any) => (
                            <div key={subCategory.id} className={styles.subCategoryItem}>
                              <input
                                type="radio"
                                name="categoryFilter"
                                id={`subcategory-${subCategory.id}`}
                                checked={selectedCategory === subCategory.id}
                                onChange={() => handleFilterCategoryChange(subCategory.id)}
                              />
                              <label htmlFor={`subcategory-${subCategory.id}`}>
                                {subCategory.name}
                              </label>
                            </div>
                          ))}
                        </div>
                      )}
                  </div>
                ))}
            </div>
            <div className={styles.filterSection}>
              <h3 className={styles.filterTitle}>Thương hiệu</h3>
              {brands.map((brand) => (
                <div key={brand.id} className={styles.filterItem}>
                  <input
                    type="checkbox"
                    id={`brand-${brand.id}`}
                    checked={selectedBrands.includes(brand.name)}
                    onChange={() => handleBrandChange(brand.name)}
                  />
                  <label htmlFor={`brand-${brand.id}`}>{brand.name}</label>
                </div>
              ))}
            </div>
            <div className={styles.filterSection}>
              <h3 className={styles.filterTitle}>Khoảng giá</h3>
              <div className={styles.filterItem}>
                <input
                  type="radio"
                  name="priceRange"
                  id="price-all"
                  checked={selectedPriceRange === 'all'}
                  onChange={() => handlePriceRangeChange('all')}
                />
                <label htmlFor="price-all">Tất cả</label>
              </div>
              <div className={styles.filterItem}>
                <input
                  type="radio"
                  name="priceRange"
                  id="price-very-low"
                  checked={selectedPriceRange === 'very-low'}
                  onChange={() => handlePriceRangeChange('very-low')}
                />
                <label htmlFor="price-very-low">Dưới 50.000 VNĐ</label>
              </div>
              <div className={styles.filterItem}>
                <input
                  type="radio"
                  name="priceRange"
                  id="price-low"
                  checked={selectedPriceRange === 'low'}
                  onChange={() => handlePriceRangeChange('low')}
                />
                <label htmlFor="price-low">50.000 - 100.000 VNĐ</label>
              </div>
              <div className={styles.filterItem}>
                <input
                  type="radio"
                  name="priceRange"
                  id="price-medium"
                  checked={selectedPriceRange === 'medium'}
                  onChange={() => handlePriceRangeChange('medium')}
                />
                <label htmlFor="price-medium">100.000 - 300.000 VNĐ</label>
              </div>
              <div className={styles.filterItem}>
                <input
                  type="radio"
                  name="priceRange"
                  id="price-medium-high"
                  checked={selectedPriceRange === 'medium-high'}
                  onChange={() => handlePriceRangeChange('medium-high')}
                />
                <label htmlFor="price-medium-high">300.000 - 500.000 VNĐ</label>
              </div>
              <div className={styles.filterItem}>
                <input
                  type="radio"
                  name="priceRange"
                  id="price-high"
                  checked={selectedPriceRange === 'high'}
                  onChange={() => handlePriceRangeChange('high')}
                />
                <label htmlFor="price-high">500.000 - 1.000.000 VNĐ</label>
              </div>
              <div className={styles.filterItem}>
                <input
                  type="radio"
                  name="priceRange"
                  id="price-very-high"
                  checked={selectedPriceRange === 'very-high'}
                  onChange={() => handlePriceRangeChange('very-high')}
                />
                <label htmlFor="price-very-high">Trên 1.000.000 VNĐ</label>
              </div>
            </div>
            <button className={styles.clearFiltersButton} onClick={handleClearFilters}>
              Xóa bộ lọc
            </button>
          </motion.aside>
        )}
      </AnimatePresence>

      <main className={styles.mainContent}>
        {/* Hero Banner */}
        <section className={styles.heroBanner}>
          <div className={styles.bannerContent}>
            <h1>Mua sắm bền vững – Kết nối giá trị</h1>
            <p>Khám phá sản phẩm nông nghiệp sạch và dịch vụ chất lượng từ TAP</p>
            <button className={styles.ctaButton}>Mua sắm ngay</button>
          </div>
        </section>



        {/* Filter Toggle Button */}
        <div className={styles.filterToggle}>
          <button
            className={styles.filterToggleBtn}
            onClick={() => setIsSidebarOpen(!isSidebarOpen)}
          >
            <FiMenu /> Bộ lọc
          </button>
        </div>

        {/* Danh mục sản phẩm */}
        <section className={styles.categoriesSection}>
          <div className={styles.container}>
            <div className={styles.sectionHeader}>
              <h2 className={styles.sectionTitle}>Danh mục sản phẩm & dịch vụ</h2>
              <div className={styles.categoryControls}>
                <button
                  className={styles.scrollButton}
                  onClick={() => {
                    const container = document.querySelector(`.${styles.categoriesGrid}`)
                    if (container) container.scrollLeft -= 300
                  }}
                >
                  <FiArrowLeft />
                </button>
                <button
                  className={styles.scrollButton}
                  onClick={() => {
                    const container = document.querySelector(`.${styles.categoriesGrid}`)
                    if (container) container.scrollLeft += 300
                  }}
                >
                  <FiArrowRight />
                </button>
              </div>
            </div>
            {categoriesLoading ? (
              <div className={styles.loadingCategories}>Đang tải danh mục...</div>
            ) : (
              <div className={styles.categoriesGrid}>
                {categories.map((category) => (
                  <motion.div
                    key={category.id}
                    className={styles.categoryCard}
                    whileHover={{ scale: 1.05 }}
                    onClick={() => handleCategoryNavigation(category.id)}
                  >
                    <div className={styles.categoryImage}>
                      <img src={category.icon} alt={category.name} />
                    </div>
                    <h3>{category.name}</h3>
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        </section>

        {/* Thương hiệu đối tác */}
        <section className={styles.brandsSection}>
          <div className={styles.container}>
            <h2 className={styles.sectionTitle}>Thương hiệu đối tác</h2>
            <div className={styles.brandsContainer}>
              <div ref={brandsGridRef} className={styles.brandsGrid}>
                {brands.map((brand) => (
                  <div key={brand.id} className={styles.brandItem}>
                    <img src={brand.logo} alt={brand.name} className={styles.brandLogo} />
                    <span className={styles.brandName}>{brand.name}</span>
                  </div>
                ))}
              </div>
              <div className={styles.scrollButtonContainer}>
                <button className={styles.scrollButton} onClick={scrollLeft}>
                  <FiArrowLeft />
                </button>
                <button className={styles.scrollButton} onClick={scrollRight}>
                  <FiArrowRight />
                </button>
              </div>
            </div>
          </div>
        </section>

        {/* Tab Navigation */}
        <div className={styles.styleTabsContainer}>
          {tabs.map((tab) => (
            <div
              key={tab.id}
              className={`${styles.styleTab} ${activeTab === tab.id ? styles.activeTab : ''} ${tab.id === 6 && autoSwitchedToImageTab ? styles.imageSearchTab : ''}`}
              onClick={() => handleTabClick(tab.id)}
            >
              {tab.name}
            </div>
          ))}
        </div>

        {/* Sản phẩm theo tab */}
        <section className={styles.productsSection}>
          <div className={styles.container}>
            <div className={styles.sectionHeader}>
              <h2>
                {activeTab === 6 &&
                imageSearchResult &&
                Array.isArray(imageSearchProducts) &&
                imageSearchProducts.length > 0
                  ? 'Kết quả tìm kiếm bằng ảnh'
                  : activeTab === 6 && imageSearchResult
                    ? 'Tìm kiếm bằng ảnh - Không tìm thấy kết quả'
                    : activeTab === 6
                      ? 'Tìm kiếm bằng ảnh'
                      : activeTab === 1
                        ? 'Tất cả sản phẩm'
                        : activeTab === 2
                          ? 'Hàng mới'
                          : activeTab === 3
                            ? 'Các sản phẩm đang giảm giá'
                            : activeTab === 4
                              ? 'Sản phẩm xếp hạng cao nhất'
                              : activeTab === 5
                                ? 'Sản phẩm xu hướng'
                                : 'Tất cả sản phẩm'}
              </h2>
              <p>{productsData?.total || filteredProducts.length} sản phẩm</p>
            </div>
            <div className={styles.productsGrid}>
              {filteredProducts.map((product) => (
                <div key={product.id} className={styles.productCard}>
                  <div
                    className={styles.productImage}
                    style={{ cursor: 'pointer' }}
                    onClick={() => navigate(ROUTES.PRODUCT_DETAIL.replace(':id', product.id))}
                  >
                    <ResponsiveImage
                      src={product.image}
                      alt={product.name}
                      aspectRatio="square"
                      objectFit="cover"
                      lazy={true}
                      sizes="(max-width: 480px) 50vw, (max-width: 768px) 33vw, (max-width: 1200px) 25vw, 20vw"
                    />
                    {hasFavoritePermission && (
                      <button
                        className={`${styles.favoriteButton} ${product.isFavorite ? styles.favorited : ''}`}
                        onClick={(e) => handleToggleFavorite(product.id, e)}
                      >
                        <FiHeart />
                      </button>
                    )}
                    {hasCartPermission && (
                      <button
                        className={styles.cartButton}
                        onClick={(e) => handleAddToCart(product, e)}
                      >
                        <FiShoppingCart />
                      </button>
                    )}
                  </div>
                  <div className={styles.productInfo}>
                    <h3 className={styles.productName}>{product.name}</h3>
                    {product.status && (
                      <p
                        className={`${styles.productStatus} ${typeof product.status === 'string' && product.status === 'active' ? styles.inStock : styles.outOfStock}`}
                      >
                        {typeof product.status === 'string'
                          ? product.status === 'active'
                            ? 'Đang bán'
                            : product.status
                          : product.status &&
                              typeof product.status === 'object' &&
                              product.status.label
                            ? product.status.label
                            : ''}
                      </p>
                    )}
                    <div className={styles.productRating}>
                      <span className={styles.starIcon}>⭐</span>
                      <span className={styles.ratingNumber}>{product.rating.toFixed(1)}</span>
                    </div>
                    <div className={styles.productPrice}>
                      <span className={styles.currentPrice}>{formatPrice(product.price)}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            {filteredProducts.length === 0 && (
              <div className={styles.noProducts}>
                {activeTab === 6 ? (
                  <div>
                    {imageSearchResult ? (
                      <div>
                        <p>Không tìm thấy sản phẩm phù hợp với hình ảnh đã tải lên</p>
                        <p style={{ fontSize: '0.9rem', color: '#666', marginTop: '8px' }}>
                          Hãy thử tải lên một hình ảnh khác hoặc sử dụng tìm kiếm bằng từ khóa
                        </p>
                      </div>
                    ) : (
                      <div>
                        <p>Chưa có kết quả tìm kiếm bằng ảnh</p>
                        <p style={{ fontSize: '0.9rem', color: '#666', marginTop: '8px' }}>
                          Hãy sử dụng nút "Tìm bằng ảnh" ở thanh tìm kiếm để tìm sản phẩm
                        </p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div>
                    <p>Không tìm thấy sản phẩm nào phù hợp với bộ lọc hiện tại</p>
                    {(debouncedSelectedCategory !== 'all' ||
                      debouncedSelectedBrands.length > 0 ||
                      debouncedSelectedPriceRange !== 'all' ||
                      debouncedSearchQuery) && (
                      <div style={{ fontSize: '0.9rem', color: '#666', marginTop: '8px' }}>
                        <button
                          onClick={handleClearFilters}
                          style={{
                            background: 'none',
                            border: '1px solid #007bff',
                            color: '#007bff',
                            padding: '6px 12px',
                            borderRadius: '4px',
                            cursor: 'pointer',
                            fontSize: '0.9rem'
                          }}
                        >
                          Xóa tất cả bộ lọc
                        </button>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Pagination */}
            {filteredProducts.length > 0 && productsData && productsData.total > pageSize && (
              <div className={styles.paginationContainer}>
                <Pagination
                  current={currentPage}
                  total={productsData.total}
                  pageSize={pageSize}
                  showSizeChanger
                  showQuickJumper
                  showTotal={(total, range) => `${range[0]}-${range[1]} của ${total} sản phẩm`}
                  onChange={(page, size) => {
                    setCurrentPage(page)
                    if (size !== pageSize) {
                      setPageSize(size)
                      setCurrentPage(1) // Reset về trang 1 khi thay đổi pageSize
                    }
                    // Scroll đến phần tabs khi chuyển trang để người dùng tiếp tục xem sản phẩm
                    scrollToTabs()
                  }}
                  pageSizeOptions={['10', '20', '50', '100']}
                  disabled={productsLoading}
                />
              </div>
            )}
          </div>
        </section>
      </main>
    </div>
  )
}

export default Commerce