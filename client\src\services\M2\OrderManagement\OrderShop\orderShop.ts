import { createApi } from '@reduxjs/toolkit/query/react'
import { axiosBaseQuery } from '../../../../libs/axios/axiosBase'

const baseUrl = 'http://localhost:8080'

// Types cho request/response
export interface OrderManagementListParams {
  page?: number;
  size?: number;
  search?: string;
  status_id?: string;
}

export interface OrderManagementListResponse {
  data?: any;
  message?: string;
  statusCode?: number;
}

export interface OrderDetailResponse {
  data?: any;
  message?: string;
  statusCode?: number;
}

export interface UpdateOrderStatusRequest {
  status_id: string;
}

export interface CancelRequestListParams {
  page?: number;
  size?: number;
  search?: string;
  status_id?: string;
}

export interface CancelRequestListResponse {
  data?: any;
  message?: string;
  statusCode?: number;
}

export const orderShopApi = createApi({
  reducerPath: 'orderShopApi',
  baseQuery: axiosBaseQuery({ baseUrl }),
  tagTypes: ['OrdersShop'],

  endpoints: (build) => ({
    // ✅ Lấy danh sách đơn hàng của shop (shop)
    getShopOrders: build.query<OrderManagementListResponse, OrderManagementListParams>({
      query: (params) => ({
        url: '/orders-management/shop', // Sử dụng endpoint hiện tại
        method: 'GET',
        params: params,
        authRequired: true,
      }),
      providesTags: ['OrdersShop'],
    }),

    // ✅ Lấy chi tiết đơn hàng của shop
    getShopOrderDetail: build.query<OrderDetailResponse, string>({
      query: (orderId: string) => ({
        url: `/orders-management/shop/${orderId}`, // Sử dụng endpoint hiện tại
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['OrdersShop'],
    }),

    // ✅ Cập nhật trạng thái đơn hàng (shop)
    updateOrderStatus: build.mutation<OrderDetailResponse, { id: string; data: UpdateOrderStatusRequest }>({
      query: ({ id, data }) => ({
        url: `/orders-management/${id}/status`, // Sử dụng endpoint hiện tại
        method: 'POST',
        data: data,
        authRequired: true,
      }),
      invalidatesTags: ['OrdersShop'],
    }),

    // ✅ Lấy danh sách yêu cầu hủy đơn hàng của shop
    getCancelOrderRequests: build.query<CancelRequestListResponse, CancelRequestListParams>({
      query: (params) => ({
        url: '/orders-management/shop/cancel-requests', // Sử dụng endpoint hiện tại
        method: 'GET',
        params: params,
        authRequired: true,
      }),
      providesTags: ['OrdersShop'],
    }),

    // ✅ Chấp nhận yêu cầu hủy đơn hàng (shop)
    acceptCancelRequest: build.mutation<OrderDetailResponse, string>({
      query: (id: string) => ({
        url: `/orders-management/${id}/accept-cancel-request`, // Sử dụng endpoint hiện tại
        method: 'POST',
        authRequired: true,
      }),
      invalidatesTags: ['OrdersShop'],
    }),
  }),
})

export const {
  useGetShopOrdersQuery,
  useGetShopOrderDetailQuery,
  useUpdateOrderStatusMutation,
  useGetCancelOrderRequestsQuery,
  useAcceptCancelRequestMutation,
} = orderShopApi
