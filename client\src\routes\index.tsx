import { Routes, Route, Navigate } from 'react-router-dom'
import Home from '../pages/Home/Home'
import Services from '../pages/Home/Services'
import News from '../pages/Home/News'
import Transfer from '../pages/Home/Transfer'
import Contact from '../pages/Home/Contact'
import type { PrivateRoute, PublicRoute } from '@/types/components/route'
import { ROUTES } from '@/constants/routes'
import SystemLayout from '@/layouts/SystemLayout'
import HomePageLayout from '@/layouts/HomePageLayout'
import NotFound from '@/pages/Error/Notfound'
import Authorise from '@/pages/Error/Authorize'
import NullLayout from '@/layouts/NullLayout'
import Network from '@/pages/Error/Network'
import ResetPassword from '@/pages/m1/ResetPassword/ResetPassword'
import OtpResetPassword from '@/pages/m1/ResetPassword/OtpResetPassword'
import ChangePassword from '@/pages/m1/ResetPassword/ChangePassword'
import UserManagement from '@/pages/m1/UsersManagement'
import ProfilePage from '@/pages/m1/UserProfile/Profile'
import Login from '@/pages/m1/Auth/Login'
import Terms from '@/pages/Home/Terms'
import Dashboard from '@/pages/m1/Dashboard'
import Commerce from '@/pages/Commerce/Commerce'
import ProductManagement from '@/pages/m2/ProductManagement/index'
import OrderManagement from '@/pages/m2/OrderManagement'
import OrderStatistics from '@/pages/m2/OrderStatistics'
import InventoryManagement from '@/pages/m2/InventoryManagement'
import CategoryManagement from '@/pages/m2/CategoryManagement'
import SmartCart from '@/pages/m2/Cart/SmartCart'
import ProtectedCart from '@/pages/m2/Cart/ProtectedCart'
import CartGuest from '@/pages/m2/Cart/CartGuest'
import Cart from '@/pages/m2/Cart'
import Orders from '@/pages/m2/Oders'
import ProtectedOrderDetail from '@/pages/m2/Oders/ProtectedOrderDetail'
import ProtectedMyOrders from '@/pages/m2/Oders/ProtectedMyOrders'
import MyOrders from '@/pages/m2/Oders/myOrder'
import Register from '@/pages/m1/Auth/Register'
import SmartProductDetail from '@/pages/m2/ProductDetail/SmartProductDetail'
import ProtectedProductDetail from '@/pages/m2/ProductDetail/ProtectedProductDetail'
import ProductDetailGuest from '@/pages/m2/ProductDetail/ProductDetailGuest'
import ProductDetail from '@/pages/m2/ProductDetail'
import ProtectedFavorites from '@/pages/m2/Favorites/ProtectedFavorites'
import Favorites from '@/pages/m2/Favorites'
import CategoryPage from '@/pages/m2/Category'
import PermissionManagement from '@/pages/m1/PermissonManagement'

const publicRoutes: PublicRoute[] = [
  { path: ROUTES.LOGIN, component: Login, layout: NullLayout },
  { path: ROUTES.FORGOT_PASSWORD, component: ResetPassword, layout: NullLayout },

  { path: ROUTES.HOME, component: Home, layout: HomePageLayout },
  { path: ROUTES.COMMERCE, component: Commerce, layout: HomePageLayout },
  { path: ROUTES.SERVICES, component: Services, layout: HomePageLayout },
  { path: ROUTES.NEWS, component: News, layout: HomePageLayout },
  { path: ROUTES.TRANSFER, component: Transfer, layout: HomePageLayout },
  { path: ROUTES.CONTACT, component: Contact, layout: HomePageLayout },
  { path: ROUTES.NOT_FOUND, component: NotFound, layout: NullLayout },
  { path: ROUTES.UNAUTHORIZE, component: Authorise, layout: NullLayout },
  { path: ROUTES.NETWORK, component: Network, layout: NullLayout },
  { path: ROUTES.OPT_RESET_PASSWORD, component: OtpResetPassword, layout: NullLayout },
  { path: ROUTES.CHANGE_PASSWORD, component: ChangePassword, layout: NullLayout },
  { path: ROUTES.REGISTER, component: Register, layout: NullLayout },
  { path: ROUTES.CART, component: SmartCart, layout: HomePageLayout },
  { path: ROUTES.PRODUCT_DETAIL, component: ProductDetail, layout: HomePageLayout },
  { path: ROUTES.CATEGORY, component: CategoryPage, layout: HomePageLayout },
]

const privateRoutes: PrivateRoute[] = [
  {
    path: ROUTES.PROFILE,
    component: ProfilePage,
    layout: HomePageLayout,
    isAuthenticated: true,
    requiredPermissions: ['PERM_USER_VIEW_SELF', 'PERM_USER_UPDATE_SELF'],
  },
  {
    path: ROUTES.DASHBOARD,
    component: Dashboard,
    layout: SystemLayout,
    isAuthenticated: true,
    requiredPermissions: ['PERM_USER_LIST'],
  },
  {
    path: ROUTES.USER_MANAGEMENT,
    component: UserManagement,
    layout: SystemLayout,
    isAuthenticated: true,
    requiredPermissions: ['PERM_USER_LIST'],
  },
  {
    path: ROUTES.PERMISSION_MANAGEMENT,
    component: PermissionManagement,
    layout: SystemLayout,
    isAuthenticated: true,
    requiredPermissions: ['PERM_USER_LIST'],
  },
  {
    path: ROUTES.PRODUCT_MANAGEMENT,
    component: ProductManagement,
    layout: SystemLayout,
    isAuthenticated: true,
    requiredPermissions: ['PERM_PRODUCT_MANAGE'],
  },
  {
    path: ROUTES.ORDER_MANAGEMENT,
    component: OrderManagement,
    layout: SystemLayout,
    isAuthenticated: true,
    requiredPermissions: ['PERM_ORDER_VIEW_ALL'],
  },
  {
    path: ROUTES.ORDER_STATISTICS,
    component: OrderStatistics,
    layout: SystemLayout,
    isAuthenticated: true,
    requiredPermissions: ['PERM_ORDER_VIEW_ALL'],
  },
  {
    path: ROUTES.INVENTORY_MANAGEMENT,
    component: InventoryManagement,
    layout: SystemLayout,
    isAuthenticated: true,
    requiredPermissions: ['PERM_INVENTORY_MANAGE'],
  },
  {
    path: ROUTES.CATEGORY_MANAGEMENT,
    component: CategoryManagement,
    layout: SystemLayout,
    isAuthenticated: true,
    requiredPermissions: ['PERM_CATEGORY_MANAGE'],
  },
  {
    path: ROUTES.ORDER_DETAIL,
    component: Orders,
    layout: HomePageLayout,
    isAuthenticated: true,
    requiredPermissions: [], // ✅ FINAL: All authenticated users can create orders
  },
  {
    path: ROUTES.MY_ORDER,
    component: MyOrders,
    layout: HomePageLayout,
    isAuthenticated: true,
    requiredPermissions: [], // ✅ FINAL: All authenticated users can view their own orders
  },
  {
    path: ROUTES.FAVORITES,
    component: Favorites,
    layout: HomePageLayout,
    isAuthenticated: true,
    requiredPermissions: [], // ✅ FINAL: All authenticated users can manage favorites
  },
]

export { publicRoutes, privateRoutes }
