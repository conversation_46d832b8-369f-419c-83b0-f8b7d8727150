import { createApi } from '@reduxjs/toolkit/query/react'
import { axiosBaseQuery } from '../../../libs/axios/axiosBase'

const baseUrl = 'http://localhost:8080'

export const categoryManagementApi = createApi({
  reducerPath: 'categoryManagementApi',
  baseQuery: axiosBaseQuery({ baseUrl }),
  tagTypes: ['Categories'],
  endpoints: (build) => ({
    // L<PERSON>y danh sách tất cả category (tree hoặc flat)
    getCategories: build.query<GetCategoriesResponse, GetCategoriesParams>({
      query: (params: GetCategoriesParams) => ({
        url: '/categories',
        method: 'GET',
        params: params,
        authOptional: true, // Gửi token nếu có, không bắt buộc
      }),
      providesTags: ['Categories'],
    }),
    // Tạo mới category
    createCategory: build.mutation<CategoryItem, CreateCategoryRequest>({
      query: (body: CreateCategoryRequest) => ({
        url: '/categories',
        method: 'POST',
        data: body,
        authRequired: true,
      }),
      invalidatesTags: ['Categories'],
    }),

    // Lấy chi tiết category theo id
    getCategoryDetail: build.query<GetCategoryDetailResponse, string>({
      query: (id: string) => ({
        url: `/categories/${id}`,
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['Categories'],
    }),
    // Cập nhật category
    updateCategory: build.mutation<CategoryItem, { id: string; body: UpdateCategoryRequest }>({
      query: ({ id, body }) => ({
        url: `/categories/${id}`,
        method: 'PUT',
        data: body,
        authRequired: true,
      }),
      invalidatesTags: ['Categories'],
    }),

    // Xóa category
    deleteCategory: build.mutation<{ message: string }, string>({
      query: (id: string) => ({
        url: `/categories/${id}`,
        method: 'DELETE',
        authRequired: true,
      }),
      invalidatesTags: ['Categories'],
    }),

    // Upload icon cho category
    uploadCategoryIcon: build.mutation<UploadIconResponse, FormData>({
      query: (formData: FormData) => ({
        url: '/categories/upload-icon',
        method: 'POST',
        data: formData,
        authRequired: true,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }),
    }),
  }),
})

// Request body cho cập nhật category
export interface UpdateCategoryRequest {
  name: string
  slug: string
  icon_url: string
  parentId?: string | null
}

// Request body cho tạo mới category
export interface CreateCategoryRequest {
  name: string
  slug: string
  icon_url: string
  parentId?: string
}

// Response cho upload icon
export interface UploadIconResponse {
  icon_url: string
  public_id: string
  message: string
}

// Response cho get categories
export interface GetCategoriesResponse {
  data: CategoryItem[]
}

// Response cho get category detail
export interface GetCategoryDetailResponse {
  data: CategoryItem
}

// Params cho get categories
export interface GetCategoriesParams {
  type?: string
}

export interface CategoryItem {
  id: string
  name: string
  slug: string
  parentId?: string
  level: number | null
  order_index: number | null
  icon_url: string | null
  is_active: boolean
  created_at: string
  children?: CategoryItem[]
}

export const {
  useGetCategoriesQuery,
  useCreateCategoryMutation,
  useGetCategoryDetailQuery,
  useUpdateCategoryMutation,
  useDeleteCategoryMutation,
  useUploadCategoryIconMutation,
} = categoryManagementApi
