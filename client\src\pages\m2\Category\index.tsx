import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { Pagination } from 'antd'
import { motion, AnimatePresence } from 'framer-motion'
import { FiArrowLeft, FiHeart, FiShoppingCart, FiMenu, FiX, FiChevronDown, FiChevronUp } from 'react-icons/fi'
import { useGetProductsHook } from '@/hooks/product/useproduct'
import { useCategoryTree } from '@/hooks/product/useCategory'
import { useGetFavoritesHook, useToggleFavoriteHook } from '@/hooks/product/useproduct'
import { useAddToCartHook, useGetCartHook } from '@/hooks/cart/useCart'
import { ROUTES } from '@/constants/routes'

import styles from './Category.module.scss'
import marketIcon from '@/assets/images/logo.png'

const CategoryPage: React.FC = () => {
  const { id: categoryId } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const auth = useSelector((state: any) => state.auth)



  // State cho pagination
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [pageSize, setPageSize] = useState<number>(20)

  // State cho bộ lọc
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)
  const [selectedBrands, setSelectedBrands] = useState<string[]>([])
  const [selectedPriceRange, setSelectedPriceRange] = useState<string>('all')
  const [debouncedSelectedBrands, setDebouncedSelectedBrands] = useState<string[]>([])
  const [debouncedSelectedPriceRange, setDebouncedSelectedPriceRange] = useState<string>('all')
  const [openCategoryIds, setOpenCategoryIds] = useState<string[]>([])
  const [sortBy, setSortBy] = useState<string>('default')

  // Lấy thông tin category (có thể fail nếu chưa đăng nhập)
  const { data: categoriesData, isLoading: categoriesLoading, error: categoriesError } = useCategoryTree({ type: 'tree' })

  // Tìm category hiện tại
  const currentCategory = Array.isArray(categoriesData)
    ? categoriesData.find((cat: any) => cat.id === categoryId)
    : null

  // Fallback category name nếu không lấy được từ API
  const categoryName = currentCategory?.name || `Danh mục ${categoryId?.slice(-4)?.toUpperCase() || ''}`

  // Dữ liệu thương hiệu mẫu
  const brands = [
    { id: '1', name: 'Samsung', logo: marketIcon },
    { id: '2', name: 'Apple', logo: marketIcon },
    { id: '3', name: 'Dell', logo: marketIcon },
    { id: '4', name: 'HP', logo: marketIcon },
    { id: '5', name: 'Lenovo', logo: marketIcon },
    { id: '6', name: 'Asus', logo: marketIcon },
    { id: '7', name: 'Nike', logo: marketIcon },
    { id: '8', name: 'Adidas', logo: marketIcon },
    { id: '9', name: 'Puma', logo: marketIcon },
    { id: '10', name: 'Bosch', logo: marketIcon },
  ]

  // Lấy sản phẩm theo category
  const {
    data: productsData,
    isLoading: productsLoading,
    error: productsError,
    refetch: refetchProducts,
  } = useGetProductsHook({
    page: currentPage,
    size: pageSize,
    category_id: categoryId,
  })

  // Favorites hooks - chỉ khi đã đăng nhập
  const { data: favoritesData } = auth?.id ? useGetFavoritesHook() : { data: null }
  const toggleFavorite = auth?.id ? useToggleFavoriteHook() : null

  // Cart hooks - chỉ khi đã đăng nhập
  const addToCart = auth?.id ? useAddToCartHook() : () => Promise.resolve(null)
  // useGetCartHook() để trigger refetch khi cart thay đổi (cho header)
  if (auth?.id) useGetCartHook()

  // Đơn giản hóa logic permissions - tất cả user đã đăng nhập đều có thể sử dụng cart và favorite
  const hasFavoritePermission = !!auth?.id // Chỉ cần đăng nhập
  const hasCartPermission = !!auth?.id // Chỉ cần đăng nhập





  // Xử lý dữ liệu sản phẩm
  const allProducts = Array.isArray(productsData?.data)
    ? productsData.data.map((item: any) => {
        const isFavorite =
          Array.isArray(favoritesData) && favoritesData.some((fav: any) => fav.id === item.id)

        // Lấy thương hiệu từ product_attribute
        const brandAttribute = Array.isArray(item.product_attribute)
          ? item.product_attribute.find((attr: any) => attr.name === 'Thương hiệu')
          : null
        const brand = brandAttribute?.value || brands[Math.floor(Math.random() * brands.length)].name

        return {
          id: item.id,
          name: item.name,
          price: Number(item.price),
          image: item.thumbnail_url || marketIcon,
          seller: item.source || 'N/A',
          rating: 5,
          reviews: 0,
          category: item.category_id,
          isFavorite: isFavorite,
          status: item.status,
          status_label: item.status_label,
          product_versions: item.product_versions || [],
          brand: brand,
        }
      })
    : []

  // Áp dụng bộ lọc
  const filteredProducts = allProducts.filter((product) => {
    // Lọc theo thương hiệu
    const brandMatch = debouncedSelectedBrands.length === 0 || debouncedSelectedBrands.includes(product.brand)

    // Lọc theo khoảng giá
    let priceMatch = true
    if (debouncedSelectedPriceRange !== 'all') {
      const price = product.price
      switch (debouncedSelectedPriceRange) {
        case 'very-low':
          priceMatch = price < 50000
          break
        case 'low':
          priceMatch = price >= 50000 && price < 100000
          break
        case 'medium':
          priceMatch = price >= 100000 && price < 300000
          break
        case 'medium-high':
          priceMatch = price >= 300000 && price < 500000
          break
        case 'high':
          priceMatch = price >= 500000 && price < 1000000
          break
        case 'very-high':
          priceMatch = price >= 1000000
          break
        default:
          priceMatch = true
      }
    }

    return brandMatch && priceMatch
  })

  // Sắp xếp sản phẩm
  const products = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return a.price - b.price
      case 'price-high':
        return b.price - a.price
      case 'name':
        return a.name.localeCompare(b.name)
      case 'rating':
        return b.rating - a.rating
      default:
        return 0
    }
  })

  // Helper function để scroll đến phần sản phẩm
  const scrollToProducts = (delay = 100) => {
    setTimeout(() => {
      const productsSection = document.querySelector(`.${styles.productsSection}`)
      if (productsSection) {
        productsSection.scrollIntoView({ behavior: 'smooth', block: 'start' })
      }
    }, delay)
  }

  // Format giá
  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price)
  }

  // Hàm xử lý bộ lọc
  const handleBrandChange = (brand: string) => {
    setSelectedBrands(
      selectedBrands.includes(brand)
        ? selectedBrands.filter((b) => b !== brand)
        : [...selectedBrands, brand]
    )
  }

  const handlePriceRangeChange = (range: string) => {
    setSelectedPriceRange(range)
  }

  const handleSortChange = (sort: string) => {
    setSortBy(sort)
  }

  const handleClearFilters = () => {
    setSelectedBrands([])
    setSelectedPriceRange('all')
    setSortBy('default')
  }

  const toggleCategory = (id: string) => {
    setOpenCategoryIds((prev) => {
      const newIds = prev.includes(id) ? prev.filter((cid) => cid !== id) : [...prev, id]
      return newIds
    })
  }

  // Xử lý toggle favorite
  const handleToggleFavorite = async (productId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    if (!toggleFavorite) return
    
    const result = await toggleFavorite(productId)
    if (result !== null) {
      // Refetch để cập nhật UI
      refetchProducts()
    }
  }

  // Xử lý thêm vào giỏ hàng
  const handleAddToCart = async (product: any, e: React.MouseEvent) => {
    e.stopPropagation()

    // Kiểm tra permission và function
    if (!hasCartPermission || !addToCart) {
      return
    }

    // Kiểm tra product_versions
    if (!product.product_versions || !Array.isArray(product.product_versions) || product.product_versions.length === 0) {
      return
    }

    const defaultVersion = product.product_versions[0]
    if (!defaultVersion || !defaultVersion.id) {
      return
    }

    // Lấy giá từ version hoặc fallback về giá sản phẩm
    const unitPrice = defaultVersion.price || product.price || 0

    try {
      const success = await addToCart({
        product_id: product.id,
        version_id: defaultVersion.id,
        quantity: 1,
        unit_price: unitPrice,
        selected_options: {},
      })

      if (success) {
        // Cart sẽ tự động refetch nhờ RTK Query invalidatesTags
      }
    } catch (error) {
      console.error('Error adding to cart:', error)
    }
  }

  // Debounced brands filter effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebouncedSelectedBrands(selectedBrands)
    }, 300)
    return () => clearTimeout(timeoutId)
  }, [selectedBrands])

  // Debounced price range filter effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebouncedSelectedPriceRange(selectedPriceRange)
    }, 300)
    return () => clearTimeout(timeoutId)
  }, [selectedPriceRange])

  // Reset về trang 1 khi thay đổi category
  useEffect(() => {
    setCurrentPage(1)
  }, [categoryId])

  // Reset về trang 1 khi thay đổi bộ lọc
  useEffect(() => {
    setCurrentPage(1)
  }, [debouncedSelectedBrands, debouncedSelectedPriceRange, sortBy])

  // Không hiển thị loading cho categories nữa vì có thể bị stuck
  // Chỉ hiển thị loading nếu đang tải sản phẩm lần đầu
  if (productsLoading && !productsData) {
    return (
      <div className={styles.categoryPage}>
        <div className={styles.loading}>Đang tải sản phẩm...</div>
      </div>
    )
  }

  // Không return early nếu không có currentCategory - vẫn cho phép hiển thị sản phẩm

  return (
    <div className={styles.categoryPage}>
      <main className={styles.mainContent}>
        {/* Breadcrumb & Category Header */}
        <section className={styles.categoryHeader}>
          <div className={styles.container}>
            <div className={styles.breadcrumb}>
              <button onClick={() => navigate(ROUTES.COMMERCE)} className={styles.breadcrumbLink}>
                Mua sắm
              </button>
              <span className={styles.breadcrumbSeparator}>/</span>
              <span className={styles.breadcrumbCurrent}>{categoryName}</span>
            </div>

            <div className={styles.categoryInfo}>
              <div className={styles.categoryIcon}>
                <img src={currentCategory?.icon_url || marketIcon} alt={categoryName} />
              </div>
              <div className={styles.categoryDetails}>
                <h1 className={styles.categoryTitle}>{categoryName}</h1>
                <p className={styles.categoryDescription}>
                  Khám phá {productsData?.total || 0} sản phẩm chất lượng trong danh mục này
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Products Section */}
        <section className={styles.productsSection}>
          <div className={styles.container}>
            {/* Filter Controls */}
            <div className={styles.filterControls}>
              <div className={styles.filterLeft}>
                <button
                  className={styles.filterToggle}
                  onClick={() => setIsSidebarOpen(true)}
                >
                  <FiMenu /> Bộ lọc
                </button>
                <span className={styles.productCount}>
                  {products.length} sản phẩm
                  {productsLoading && ' (đang tải...)'}
                  {productsError ? ' (lỗi)' : ''}
                </span>
              </div>
              <div className={styles.filterRight}>
                <select
                  value={sortBy}
                  onChange={(e) => handleSortChange(e.target.value)}
                  className={styles.sortSelect}
                >
                  <option value="default">Sắp xếp mặc định</option>
                  <option value="price-low">Giá thấp đến cao</option>
                  <option value="price-high">Giá cao đến thấp</option>
                  <option value="name">Tên A-Z</option>
                  <option value="rating">Đánh giá cao nhất</option>
                </select>
              </div>
            </div>


            {productsLoading ? (
              <div className={styles.loading}>Đang tải sản phẩm...</div>
            ) : productsError ? (
              <div className={styles.error}>
                <div className={styles.errorIcon}>⚠️</div>
                <h3>Không thể tải sản phẩm</h3>
                <p>Vui lòng thử lại sau hoặc kiểm tra kết nối mạng.</p>
                <button onClick={() => refetchProducts()} className={styles.retryButton}>
                  Thử lại
                </button>
              </div>
            ) : products.length > 0 ? (
              <>
                <div className={styles.productsGrid}>
                  {products.map((product) => (
                    <motion.div
                      key={product.id}
                      className={styles.productCard}
                      whileHover={{ scale: 1.02 }}
                      onClick={() => navigate(ROUTES.PRODUCT_DETAIL.replace(':id', product.id))}
                    >
                      <div className={styles.productImage}>
                        <img src={product.image} alt={product.name} />
                        {hasFavoritePermission && (
                          <button
                            className={`${styles.favoriteButton} ${product.isFavorite ? styles.favorited : ''}`}
                            onClick={(e) => handleToggleFavorite(product.id, e)}
                          >
                            <FiHeart />
                          </button>
                        )}
                        {hasCartPermission && (
                          <button
                            className={styles.cartButton}
                            onClick={(e) => handleAddToCart(product, e)}
                          >
                            <FiShoppingCart />
                          </button>
                        )}
                      </div>
                      <div className={styles.productInfo}>
                        <h3 className={styles.productName}>{product.name}</h3>
                        <div className={styles.productRating}>
                          <span className={styles.starIcon}>⭐</span>
                          <span className={styles.ratingNumber}>{product.rating.toFixed(1)}</span>
                        </div>
                        <div className={styles.productPrice}>
                          <span className={styles.currentPrice}>{formatPrice(product.price)}</span>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>

                {/* Pagination */}
                {productsData && productsData.total > pageSize && (
                  <div className={styles.paginationContainer}>
                    <Pagination
                      current={currentPage}
                      total={productsData.total}
                      pageSize={pageSize}
                      showSizeChanger
                      showQuickJumper
                      showTotal={(total, range) => `${range[0]}-${range[1]} của ${total} sản phẩm`}
                      onChange={(page, size) => {
                        setCurrentPage(page)
                        if (size !== pageSize) {
                          setPageSize(size)
                          setCurrentPage(1)
                        }
                        scrollToProducts()
                      }}
                      pageSizeOptions={['10', '20', '50', '100']}
                      disabled={productsLoading}
                    />
                  </div>
                )}
              </>
            ) : (
              <div className={styles.noProducts}>
                <div className={styles.noProductsIcon}>📦</div>
                <h3>Chưa có sản phẩm nào</h3>
                <p>Danh mục này hiện chưa có sản phẩm. Hãy quay lại sau hoặc khám phá các danh mục khác.</p>
                <button onClick={() => navigate(ROUTES.COMMERCE)} className={styles.backButton}>
                  <FiArrowLeft /> Quay về mua sắm
                </button>
              </div>
            )}
          </div>
        </section>
      </main>

      {/* Filter Sidebar */}
      <AnimatePresence>
        {isSidebarOpen && (
          <motion.aside
            className={styles.sidebar}
            initial={{ x: '-100%' }}
            animate={{ x: 0 }}
            exit={{ x: '-100%' }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
          >
            <div className={styles.sidebarHeader}>
              <h2>Bộ lọc sản phẩm</h2>
              <button className={styles.sidebarClose} onClick={() => setIsSidebarOpen(false)}>
                <FiX />
              </button>
            </div>

            {/* Thương hiệu */}
            <div className={styles.filterSection}>
              <h3 className={styles.filterTitle}>Thương hiệu</h3>
              {brands.map((brand) => (
                <div key={brand.id} className={styles.filterItem}>
                  <input
                    type="checkbox"
                    id={`brand-${brand.id}`}
                    checked={selectedBrands.includes(brand.name)}
                    onChange={() => handleBrandChange(brand.name)}
                  />
                  <label htmlFor={`brand-${brand.id}`}>{brand.name}</label>
                </div>
              ))}
            </div>

            {/* Khoảng giá */}
            <div className={styles.filterSection}>
              <h3 className={styles.filterTitle}>Khoảng giá</h3>
              <div className={styles.filterItem}>
                <input
                  type="radio"
                  name="priceRange"
                  id="price-all"
                  checked={selectedPriceRange === 'all'}
                  onChange={() => handlePriceRangeChange('all')}
                />
                <label htmlFor="price-all">Tất cả</label>
              </div>
              <div className={styles.filterItem}>
                <input
                  type="radio"
                  name="priceRange"
                  id="price-very-low"
                  checked={selectedPriceRange === 'very-low'}
                  onChange={() => handlePriceRangeChange('very-low')}
                />
                <label htmlFor="price-very-low">Dưới 50.000 VNĐ</label>
              </div>
              <div className={styles.filterItem}>
                <input
                  type="radio"
                  name="priceRange"
                  id="price-low"
                  checked={selectedPriceRange === 'low'}
                  onChange={() => handlePriceRangeChange('low')}
                />
                <label htmlFor="price-low">50.000 - 100.000 VNĐ</label>
              </div>
              <div className={styles.filterItem}>
                <input
                  type="radio"
                  name="priceRange"
                  id="price-medium"
                  checked={selectedPriceRange === 'medium'}
                  onChange={() => handlePriceRangeChange('medium')}
                />
                <label htmlFor="price-medium">100.000 - 300.000 VNĐ</label>
              </div>
              <div className={styles.filterItem}>
                <input
                  type="radio"
                  name="priceRange"
                  id="price-medium-high"
                  checked={selectedPriceRange === 'medium-high'}
                  onChange={() => handlePriceRangeChange('medium-high')}
                />
                <label htmlFor="price-medium-high">300.000 - 500.000 VNĐ</label>
              </div>
              <div className={styles.filterItem}>
                <input
                  type="radio"
                  name="priceRange"
                  id="price-high"
                  checked={selectedPriceRange === 'high'}
                  onChange={() => handlePriceRangeChange('high')}
                />
                <label htmlFor="price-high">500.000 - 1.000.000 VNĐ</label>
              </div>
              <div className={styles.filterItem}>
                <input
                  type="radio"
                  name="priceRange"
                  id="price-very-high"
                  checked={selectedPriceRange === 'very-high'}
                  onChange={() => handlePriceRangeChange('very-high')}
                />
                <label htmlFor="price-very-high">Trên 1.000.000 VNĐ</label>
              </div>
            </div>

            <button className={styles.clearFiltersButton} onClick={handleClearFilters}>
              Xóa bộ lọc
            </button>
          </motion.aside>
        )}
      </AnimatePresence>
    </div>
  )
}

export default CategoryPage
