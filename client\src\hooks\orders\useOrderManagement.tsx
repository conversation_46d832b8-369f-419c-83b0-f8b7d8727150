import {
  useGetAdminOrdersQuery,
  useGetAdminOrderDetailQuery,
  useGetSellerOrdersQuery,
  useUpdateOrderStatusMutation,
  useGetCancelRequestsQuery,
  useAcceptCancelRequestMutation,
  useGetSellerCancelRequestsQuery,
  useGetSellerOrderDetailQuery,
  type OrderManagementListParams,
  type UpdateOrderStatusRequest,
  type CancelRequestListParams,
} from '@/services/M2/OrderManagement/orderManagement';
import { useToast } from '@/components/Toast';
import { useEffect } from 'react';
import { useLoadingStore } from '@/utils/loadingStore';

// GET: Danh sách đơn hàng admin
export const useGetAdminOrdersHook = (params: OrderManagementListParams) => {
  const { data, error, isLoading, refetch, isFetching } = useGetAdminOrdersQuery(params);
  const { setLoading } = useLoadingStore();
  useEffect(() => {
    setLoading(isLoading || isFetching);
  }, [isLoading, isFetching, setLoading]);
  return { data, isLoading, error, refetch };
};

// GET: Chi tiết đơn hàng admin
export const useGetAdminOrderDetailHook = (id: string) => {
  const { data, error, isLoading, refetch, isFetching } = useGetAdminOrderDetailQuery(id);
  const { setLoading } = useLoadingStore();
  useEffect(() => {
    setLoading(isLoading || isFetching);
  }, [isLoading, isFetching, setLoading]);
  return { data, isLoading, error, refetch };
};

// GET: Danh sách đơn hàng seller
export const useGetSellerOrdersHook = (params: OrderManagementListParams) => {
  const { data, error, isLoading, refetch, isFetching } = useGetSellerOrdersQuery(params);
  const { setLoading } = useLoadingStore();
  useEffect(() => {
    setLoading(isLoading || isFetching);
  }, [isLoading, isFetching, setLoading]);
  return { data, isLoading, error, refetch };
};

// GET: Danh sách yêu cầu hủy đơn
export const useGetCancelRequestsHook = (params: CancelRequestListParams) => {
  const { data, error, isLoading, refetch, isFetching } = useGetCancelRequestsQuery(params);
  const { setLoading } = useLoadingStore();
  useEffect(() => {
    setLoading(isLoading || isFetching);
  }, [isLoading, isFetching, setLoading]);
  return { data, isLoading, error, refetch };
};

// GET: Danh sách yêu cầu hủy đơn của seller
export const useGetSellerCancelRequestsHook = (params: CancelRequestListParams) => {
  const { data, error, isLoading, refetch, isFetching } = useGetSellerCancelRequestsQuery(params);
  const { setLoading } = useLoadingStore();
  useEffect(() => {
    setLoading(isLoading || isFetching);
  }, [isLoading, isFetching, setLoading]);
  return { data, isLoading, error, refetch };
};

// GET: Chi tiết đơn hàng của seller
export const useGetSellerOrderDetailHook = (order_id: string) => {
  const { data, error, isLoading, refetch, isFetching } = useGetSellerOrderDetailQuery(order_id);
  const { setLoading } = useLoadingStore();
  useEffect(() => {
    setLoading(isLoading || isFetching);
  }, [isLoading, isFetching, setLoading]);
  return { data, isLoading, error, refetch };
};

// POST: Cập nhật trạng thái đơn hàng
export const useUpdateOrderStatusHook = () => {
  const { showSuccess, showError } = useToast();
  const [updateFunc] = useUpdateOrderStatusMutation();
  const update = async (id: string, data: UpdateOrderStatusRequest): Promise<boolean> => {
    try {
      const res = await updateFunc({ id, data });
      if (!res?.error) {
        showSuccess('Cập nhật trạng thái đơn hàng thành công');
        return true;
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message;
        showError('Cập nhật trạng thái thất bại ' + messageDes);
        return false;
      }
    } catch (e) {
      showError('Cập nhật trạng thái thất bại ' + e);
      return false;
    }
  };
  return update;
};

// POST: Duyệt yêu cầu hủy đơn
export const useAcceptCancelRequestHook = () => {
  const { showSuccess, showError } = useToast();
  const [acceptFunc] = useAcceptCancelRequestMutation();
  const accept = async (id: string): Promise<boolean> => {
    try {
      const res = await acceptFunc(id);
      if (!res?.error) {
        showSuccess('Duyệt yêu cầu hủy đơn thành công');
        return true;
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message;
        showError('Duyệt yêu cầu hủy đơn thất bại ' + messageDes);
        return false;
      }
    } catch (e) {
      showError('Duyệt yêu cầu hủy đơn thất bại ' + e);
      return false;
    }
  };
  return accept;
};
