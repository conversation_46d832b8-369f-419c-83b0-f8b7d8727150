{"intents": {"Login.Request": {"utterances": ["<PERSON><PERSON><PERSON> muốn đ<PERSON>ng <PERSON>h<PERSON>p", "<PERSON>àm sao để đăng nhập tài <PERSON>?", "Hướng dẫn cách đăng nhập", "<PERSON><PERSON><PERSON> không biết cách vào hệ thống", "<PERSON><PERSON><PERSON> nhập tà<PERSON>n như thế nào", "<PERSON><PERSON><PERSON><PERSON> tôi đăng nh<PERSON>p", "<PERSON><PERSON><PERSON> thế nào để truy cập tài k<PERSON>n", "<PERSON><PERSON><PERSON> cần đăng nhập để tiếp tục"], "answers": ["<PERSON><PERSON><PERSON> h<PERSON><PERSON> nhập email và mật khẩu tại trang Đ<PERSON>ng nhập, sau đ<PERSON> bấm nút “<PERSON><PERSON><PERSON> nhập” để truy cập.", "<PERSON><PERSON> đăng nhập, bạ<PERSON> và<PERSON> mục <PERSON> nh<PERSON>, nhập thông tin tài khoản và nhấn “<PERSON><PERSON><PERSON> nhập” nhé."], "suggestedReplies": ["<PERSON><PERSON><PERSON> quên mật kh<PERSON>u", "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài k<PERSON>n", "<PERSON><PERSON><PERSON> hệ hỗ trợ"]}, "Login.WrongPassword": {"utterances": ["<PERSON><PERSON><PERSON> kh<PERSON>u không đúng", "<PERSON><PERSON><PERSON> nh<PERSON>p sai mật kh<PERSON>u", "Lỗi mật khẩu", "Sai password", "<PERSON><PERSON><PERSON><PERSON> thể đăng nhập vì mật khẩu", "<PERSON><PERSON> thống báo mật kh<PERSON>u không hợp lệ", "Password không khớp", "<PERSON><PERSON><PERSON>h<PERSON>u bị từ chối"], "answers": ["Bạn vui lòng kiểm tra lại mật khẩu. <PERSON><PERSON><PERSON> đã quên, bạn có thể đặt lại mật khẩu bằng tính năng “Quên mật khẩu”.", "<PERSON><PERSON>t khẩu bạn nhập không đúng. <PERSON><PERSON><PERSON> thử lại hoặc chọn “Quên mật khẩu” để tạo mật khẩu mới."], "suggestedReplies": ["<PERSON><PERSON><PERSON><PERSON> mật k<PERSON>u", "<PERSON><PERSON><PERSON> lại", "<PERSON><PERSON><PERSON> hệ hỗ trợ"]}, "Login.Require2FA": {"utterances": ["<PERSON><PERSON> thống yêu cầu mã 2FA", "<PERSON><PERSON><PERSON> cần mã xác thực hai lớp", "Làm sao l<PERSON>y mã 2FA?", "<PERSON><PERSON><PERSON><PERSON> nhận đ<PERSON><PERSON>c mã xác thực", "<PERSON><PERSON><PERSON> c<PERSON>u mã xác minh hai b<PERSON>ớc", "<PERSON><PERSON> xác thực không hợp lệ", "<PERSON><PERSON>n mã b<PERSON><PERSON> mật", "2FA không hoạt động"], "answers": ["<PERSON>ạn kiểm tra mã 2FA được gửi qua email hoặc SMS và nhập vào ô mã xác thực.", "<PERSON><PERSON><PERSON> không nhận đư<PERSON><PERSON> mã, bạn có thể bấm “Gửi lại mã” hoặc kiểm tra lại cấu hình bảo mật hai lớp."], "suggestedReplies": ["<PERSON><PERSON>i lại mã x<PERSON>c thực", "Hướng dẫn tắt 2FA", "<PERSON><PERSON><PERSON> hệ hỗ trợ"]}, "Login.UsernameNotFound": {"utterances": ["<PERSON><PERSON><PERSON> k<PERSON>n không tồn tại", "<PERSON><PERSON><PERSON><PERSON> tìm thấy tên đăng nhập", "Username sai", "<PERSON><PERSON> chưa đ<PERSON><PERSON><PERSON> đ<PERSON>ng ký", "<PERSON><PERSON><PERSON><PERSON> có tài kho<PERSON>n này", "<PERSON><PERSON><PERSON> không có tài k<PERSON>n", "<PERSON><PERSON> thống báo không tìm thấy tài k<PERSON>n", "<PERSON><PERSON><PERSON> đ<PERSON>ng nh<PERSON>p không hợp lệ"], "answers": ["<PERSON><PERSON><PERSON><PERSON> tìm thấy tài khoản của bạn. <PERSON>ui lòng kiểm tra lại tên đăng nhập hoặc đăng ký tài khoản mới.", "Email hoặc tên đăng nhập chưa được dùng để đăng ký. Bạn có thể tạo tài khoản mới hoặc liên hệ hỗ trợ."], "suggestedReplies": ["<PERSON><PERSON><PERSON> ký tài k<PERSON>n", "<PERSON><PERSON><PERSON><PERSON> mật k<PERSON>u", "<PERSON><PERSON><PERSON> hệ hỗ trợ"]}, "Register.Request": {"utterances": ["<PERSON><PERSON><PERSON> muốn đăng ký tài kho<PERSON>n", "<PERSON><PERSON><PERSON> đăng ký tài k<PERSON>n?", "<PERSON><PERSON>m sao để tạo tài khoản mới", "Mở tài khoản mới", "Hướng dẫn đăng ký", "<PERSON><PERSON><PERSON> tài k<PERSON>n như thế nào", "<PERSON><PERSON><PERSON> c<PERSON>n đ<PERSON>ng ký", "<PERSON><PERSON>ng ký để sử dụng"], "answers": ["Bạn vào trang Đăng ký, nhập email, mật khẩu và các thông tin yêu cầu rồi nhấn “Đăng ký” để hoàn tất.", "<PERSON><PERSON> đ<PERSON>ng ký, bạ<PERSON> cung cấp email và mật khẩu, sau đ<PERSON> xác minh email để kích hoạt tài k<PERSON>."], "suggestedReplies": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> mật k<PERSON>u", "<PERSON><PERSON><PERSON> hệ hỗ trợ"]}, "Register.EmailExists": {"utterances": ["<PERSON>ail này đã đư<PERSON>c sử dụng", "<PERSON><PERSON> đã tồn tại", "<PERSON><PERSON><PERSON>ng thể đăng ký vì email đã có", "<PERSON><PERSON> thống báo email trùng", "<PERSON>ail đã đăng ký", "<PERSON><PERSON><PERSON><PERSON> thể dùng email này", "<PERSON><PERSON> của tôi bị trùng", "T<PERSON><PERSON> không thể tạo tài khoản vì email"], "answers": ["Email này đã được đăng ký trước đó. Bạn có thể đăng nhập hoặc sử dụng email khác để đăng ký.", "<PERSON>ail bạn cung cấp đã tồn tại. <PERSON><PERSON><PERSON> quên mật khẩu, h<PERSON><PERSON> chọn “Quên mật khẩu” để đặt lại."], "suggestedReplies": ["<PERSON><PERSON><PERSON><PERSON> mật k<PERSON>u", "<PERSON><PERSON><PERSON>", "Sử dụng email khác"]}, "Support.ContactHuman": {"utterances": ["Tôi muốn nói chuyện với nhân viên hỗ trợ", "<PERSON><PERSON><PERSON> n<PERSON>i với tư vấn viên", "Cho tôi gặp bộ phận hỗ trợ", "<PERSON><PERSON><PERSON> cần hỗ trợ trực tiếp", "<PERSON><PERSON><PERSON> h<PERSON> chăm sóc kh<PERSON>ch hàng", "<PERSON><PERSON><PERSON> muốn gặp người thật", "<PERSON><PERSON><PERSON> n<PERSON>i với nhân viên", "Hỗ trợ qua nhân viên"], "answers": ["<PERSON><PERSON> kết nối bạn với bộ phận hỗ trợ. <PERSON><PERSON> lòng chờ trong gi<PERSON>y lát.", "Tôi sẽ chuyển yêu cầu của bạn đến nhân viên hỗ trợ ngay bây giờ."], "suggestedReplies": ["Quay lại menu chính", "<PERSON><PERSON><PERSON><PERSON> mật k<PERSON>u", "Đ<PERSON>ng ký mới"]}}, "entities": {}}