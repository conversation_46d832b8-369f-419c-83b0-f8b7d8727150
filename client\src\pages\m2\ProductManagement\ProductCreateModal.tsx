import React, { useState, useRef } from 'react'
import {
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  TreeSelect,
  Upload,
  Button,
  Row,
  Col,
  Checkbox,
  message,
  Card,
} from 'antd'
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons'
import ModalButtonGroup from '@/components/ModalButtonGroup'
import type { CategoryItem, ProductImage } from '@/services/M2/ProductManagement/productManagement'

import styles from './ProductManagement.module.scss'

const { Option } = Select
const { TextArea } = Input

import {
  useCreateProductWithImagesHook,
  useUploadProductImagesHook,
} from '@/hooks/product/useproduct'
interface ProductCreateModalProps {
  open: boolean
  onCancel: () => void
  form: any
  treeCategories: any[]
  categoriesLoading: boolean
}

// Định nghĩa type cho response khi upload ảnh
export type UploadImageResponse = {
  image_url: string
  image_id: string
  id: string
  status_id: string
}

// AntD UploadFile type (partial, for our use)

const ProductCreateModal: React.FC<ProductCreateModalProps> = ({
  open,
  onCancel,
  form,
  treeCategories,
  categoriesLoading,
}) => {
  const [fileList, setFileList] = useState<any[]>([])
  const [uploading, setUploading] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  // Lưu danh sách ảnh đã upload thành công
  const [uploadedImages, setUploadedImages] = useState<UploadImageResponse[]>([])
  // Ref để lưu số lượng fileList trước đó
  const prevFileListLength = useRef(0)
  const uploadImages = useUploadProductImagesHook()
  const createProductWithImages = useCreateProductWithImagesHook()

  const handleImageChange = async (info: { fileList: any[] }) => {
    // Chỉ upload khi số lượng fileList tăng lên (user vừa chọn batch mới)
    const prevLen = prevFileListLength.current
    const currLen = info.fileList.length
    prevFileListLength.current = currLen
    // Nếu số lượng fileList tăng lên, nghĩa là vừa chọn thêm ảnh mới
    if (currLen > prevLen) {
      // Lấy các file mới chưa upload
      const newFiles = info.fileList.filter(
        (f) => f.originFileObj && !f.uploaded && f.status !== 'uploading'
      )
      if (newFiles.length > 0) {
        // Mark new files as uploading
        let updatedFileList = info.fileList.map((f) => {
          if (newFiles.some((nf) => nf.uid === f.uid)) {
            // Đảm bảo name luôn là string
            return {
              ...f,
              status: 'uploading',
              name: f.name || (f.originFileObj ? f.originFileObj.name : f.uid),
            }
          }
          return { ...f, name: f.name || (f.originFileObj ? f.originFileObj.name : f.uid) }
        })
        setFileList(updatedFileList)
        setUploading(true)
        try {
          const files = newFiles.map((f) => f.originFileObj!)
          const result = await uploadImages(files)
          // Type guard for UploadImageResponse
          function hasImageId(img: any): img is UploadImageResponse {
            return !!img && typeof img.image_id === 'string' && typeof img.image_url === 'string'
          }
          const validResult = (result || []).filter(hasImageId) as unknown as UploadImageResponse[]
          // Map upload results to fileList
          updatedFileList = updatedFileList.map((f) => {
            if (f.originFileObj && f.status === 'uploading') {
              let found: UploadImageResponse | undefined = undefined
              if (validResult && Array.isArray(validResult)) {
                const idx = newFiles.findIndex((nf) => nf.uid === f.uid)
                if (idx > -1 && validResult[idx]) {
                  found = validResult[idx]
                } else {
                  found = validResult.find(
                    (img) =>
                      img && img.image_url && f.name && img.image_url.includes(f.name.split('.')[0])
                  )
                }
              }
              if (found && found.image_url && found.image_id) {
                // Lưu riêng public_image_id (cloudinary id) để không bị ghi đè
                return {
                  ...f,
                  url: found.image_url,
                  image_id: found.image_id, // cloudinary id
                  public_image_id: found.image_id, // cloudinary id
                  id: found.id, // uuid
                  status_id: found.status_id,
                  uploaded: true,
                  status: 'done',
                }
              } else {
                return { ...f, status: 'error' }
              }
            }
            return f
          })
          // Remove duplicates in uploadedImages by image_id
          setUploadedImages((prev) => {
            const ids = new Set(prev.map((img) => img.image_id))
            return [...prev, ...validResult.filter((img) => !ids.has(img.image_id))]
          })
          setFileList(updatedFileList)
        } finally {
          setUploading(false)
        }
      }
    } else {
      // Đảm bảo name luôn là string cho mọi file
      setFileList(
        info.fileList.map((f) => ({
          ...f,
          name: f.name || (f.originFileObj ? f.originFileObj.name : f.uid),
        }))
      )
    }
    // Always update preview images (no-op)
  }

  const handleRemove = (file: any) => {
    setFileList((prev) => prev.filter((f) => f.uid !== file.uid))
  }

  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/')
    const isLt5M = file.size / 1024 / 1024 < 5
    if (!isImage) {
      message.error('Chỉ được chọn file ảnh!')
      return Upload.LIST_IGNORE
    }
    if (!isLt5M) {
      message.error('Ảnh phải nhỏ hơn 5MB!')
      return Upload.LIST_IGNORE
    }
    return false // Ngăn Upload tự động upload, chỉ lưu vào fileList
  }

  const handleSubmit = async (values: any) => {
    if (isSubmitting || uploading) {
      message.warning('Vui lòng chờ upload ảnh xong!')
      return
    }
    setIsSubmitting(true)
    try {
      // Lấy ảnh đã upload thành công từ fileList, nếu không có thì lấy từ uploadedImages
      // Chỉ lấy ảnh từ uploadedImages (response upload), không lấy từ fileList
      let allImages = uploadedImages.map((img, idx) => ({
        image_url: img.image_url,
        public_image_id: img.image_id, // cloudinary id
        is_thumbnail: idx === 0,
      }))
      if (allImages.length === 0) {
        message.error('Bạn cần upload ít nhất 1 ảnh sản phẩm!')
        setIsSubmitting(false)
        return
      }
      const productData = {
        name: values.name,
        description: values.description || '',
        price: Number(values.price),
        unit: values.unit,
        category_id: values.category_id,
        product_attribute: values.product_attribute || [],
        product_versions: values.product_versions || [],
        product_images: allImages,
      }

      // Nếu đã có ảnh upload thành công thì chỉ truyền productData
      // Nếu chưa có, truyền thêm imageFiles là các file gốc chưa upload
      let success = false
      if (allImages.length > 0) {
        console.log('Submitting product data:', productData)
        success = await createProductWithImages(productData)
      } else {
        // Lấy các file gốc chưa upload
        const imageFiles = fileList.filter((f) => f.originFileObj).map((f) => f.originFileObj!)
        console.log('Submitting product data (with imageFiles):', productData, imageFiles)
        success = await createProductWithImages(productData, imageFiles)
      }
      if (success) {
        onCancel()
        form.resetFields()
        setFileList([])
        setUploadedImages([])
      }
    } catch (error) {
      message.error('Có lỗi xảy ra!')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Modal
      title="Thêm sản phẩm mới"
      open={open}
      onCancel={onCancel}
      footer={null}
      width={900}
      className={styles.productModal}
      maskClosable={false}
      keyboard={false}
    >
      <Form form={form} onFinish={handleSubmit} layout="vertical" className={styles.productForm}>
        <Row gutter={16}>
          <Col xs={24} sm={12}>
            <Form.Item
              name="name"
              label="Tên sản phẩm"
              rules={[{ required: true, message: 'Vui lòng nhập tên sản phẩm!' }]}
            >
              <Input placeholder="Nhập tên sản phẩm" />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="unit"
              label="Đơn vị"
              rules={[{ required: true, message: 'Vui lòng nhập đơn vị!' }]}
            >
              <Input placeholder="kg, cái, hộp..." />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col xs={24} sm={12}>
            <Form.Item
              name="category_id"
              label="Danh mục"
              rules={[{ required: true, message: 'Vui lòng chọn danh mục!' }]}
            >
              <TreeSelect
                style={{ width: '100%' }}
                dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                treeData={treeCategories}
                placeholder="Chọn danh mục"
                treeDefaultExpandAll
                loading={categoriesLoading}
                showSearch
                treeNodeFilterProp="title"
                allowClear
                notFoundContent={categoriesLoading ? 'Đang tải...' : 'Không có danh mục'}
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="price"
              label="Giá bán"
              rules={[{ required: true, message: 'Vui lòng nhập giá bán!' }]}
            >
              <InputNumber
                min={0}
                placeholder="Nhập giá bán"
                style={{ width: '100%' }}
                formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item name="description" label="Mô tả sản phẩm">
          <TextArea rows={3} placeholder="Nhập mô tả sản phẩm" />
        </Form.Item>

        <Form.Item
          name="product_images"
          label="Hình ảnh sản phẩm"
          extra={`Đã chọn ${fileList.length} ảnh. Tối đa 10 ảnh, mỗi ảnh 5MB.`}
        >
          <Upload
            fileList={fileList as any} // ép kiểu cho AntD Upload
            listType="picture-card"
            multiple
            maxCount={10}
            accept="image/*"
            beforeUpload={beforeUpload}
            onRemove={handleRemove}
            onChange={handleImageChange}
            disabled={isSubmitting || uploading}
          >
            <div>
              <PlusOutlined />
              <div style={{ marginTop: 8 }}>Chọn ảnh</div>
            </div>
          </Upload>
        </Form.Item>

        <Form.List name="product_attribute">
          {(fields, { add, remove }) => (
            <>
              <Form.Item label="Thuộc tính sản phẩm (Tùy chọn)">
                <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                  Thêm thuộc tính
                </Button>
              </Form.Item>
              {fields.map(({ key, name, ...restField }) => (
                <Row key={key} gutter={16} align="middle">
                  <Col span={6}>
                    <Form.Item {...restField} name={[name, 'attribute_name']}>
                      <Input placeholder="Tên thuộc tính" />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item {...restField} name={[name, 'value']}>
                      <Input placeholder="Giá trị" />
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    <Form.Item {...restField} name={[name, 'unit']}>
                      <Input placeholder="Đơn vị" />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      {...restField}
                      name={[name, 'is_searchable']}
                      valuePropName="checked"
                    >
                      <Checkbox>Có thể tìm kiếm</Checkbox>
                    </Form.Item>
                  </Col>
                  <Col span={2}>
                    <MinusCircleOutlined onClick={() => remove(name)} />
                  </Col>
                </Row>
              ))}
            </>
          )}
        </Form.List>

        <Form.List name="product_versions">
          {(fields, { add, remove }) => (
            <>
              <Form.Item label="Phiên bản sản phẩm (Tùy chọn)">
                <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                  Thêm phiên bản
                </Button>
              </Form.Item>
              {fields.map(({ key, name, ...restField }) => (
                <Card key={key} size="small" style={{ marginBottom: 16 }}>
                  <Row gutter={16}>
                    <Col xs={24} sm={12}>
                      <Form.Item {...restField} name={[name, 'name']} label="Tên phiên bản">
                        <Input placeholder="Phiên bản tiêu chuẩn" />
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={12}>
                      <Form.Item {...restField} name={[name, 'sku_code']} label="Mã SKU">
                        <Input placeholder="SKU-001" />
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={8}>
                      <Form.Item {...restField} name={[name, 'price']} label="Giá bán">
                        <InputNumber min={0} placeholder="0" style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={8}>
                      <Form.Item {...restField} name={[name, 'stock']} label="Tồn kho">
                        <InputNumber min={0} placeholder="0" style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={8}>
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                        }}
                      >
                        <span>Tùy chọn</span>
                        <Button
                          variant="outlined"
                          icon={<MinusCircleOutlined />}
                          onClick={() => remove(name)}
                          size="small"
                        >
                          Xóa
                        </Button>
                      </div>
                    </Col>
                  </Row>
                </Card>
              ))}
            </>
          )}
        </Form.List>

        <Form.Item>
          <ModalButtonGroup
            onCancel={onCancel}
            onConfirm={() => form.submit()}
            cancelText="Hủy bỏ"
            confirmText={isSubmitting || uploading ? 'Đang xử lý...' : 'Thêm mới'}
            confirmLoading={isSubmitting || uploading}
            disabled={isSubmitting || uploading}
            confirmVariant="primary"
          />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default ProductCreateModal
