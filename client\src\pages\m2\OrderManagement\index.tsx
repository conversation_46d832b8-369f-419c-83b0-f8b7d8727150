import React, { useState, useEffect, useMemo } from 'react';
import {
  Table, Modal, Form, Input, Select, Tag, Space, Card, Statistic, Row, Col, Badge,
  Tooltip, Avatar, Steps, Descriptions, Divider, message, Popconfirm, Drawer, Collapse, DatePicker, Radio, Alert, Spin
} from 'antd';
import Button from '@/components/Button';
import ModalButtonGroup from '@/components/ModalButtonGroup';
import {
  ShoppingCartOutlined, UserOutlined, PhoneOutlined, EnvironmentOutlined, PrinterOutlined,
  EyeOutlined, EditOutlined, MessageOutlined, FilterOutlined, SearchOutlined, ExportOutlined,
  MailOutlined, BellOutlined, CalendarOutlined, DollarOutlined, TruckOutlined, CheckCircleOutlined,
  ClockCircleOutlined, CloseCircleOutlined, MenuOutlined, AppstoreOutlined, UnorderedListOutlined,
  FileTextOutlined, ShoppingOutlined, CarOutlined, HomeOutlined, ExclamationCircleOutlined
} from '@ant-design/icons';
import styles from './OrderManagement.module.scss';
import logo from '@/assets/images/logo.png';

// Import các hooks và types cần thiết từ service
import {
  useGetAdminOrdersHook,
  useUpdateOrderStatusHook,
  useGetCancelRequestsHook,
  useAcceptCancelRequestHook,
} from '@/hooks/orders/useOrderManagement';

// Sử dụng hook từ user order thay vì admin order
import { useGetOrderByIdHook } from '@/hooks/orders/useOrder';

import { type UpdateOrderStatusRequest } from '@/services/M2/OrderManagement/orderManagement';
import { useGetStatusesByTypeHook } from '@/hooks/product/useproduct';

const { Option } = Select;
const { TextArea } = Input;
const { RangePicker } = DatePicker;
const { Panel } = Collapse;
const { Step } = Steps;

// Helper hook để debounce giá trị
const useDebounce = (value: string, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  return debouncedValue;
};

// ✅ THÊM: Định nghĩa các bước chuẩn của quy trình đơn hàng
const ORDER_STEPS = [
  {
    title: 'Đơn hàng mới',
    description: 'Đơn hàng đã được tạo',
    icon: <FileTextOutlined />,
    status: ['Chờ xác nhận', 'PENDING', 'pending']
  },
  {
    title: 'Đã xác nhận',
    description: 'Đơn hàng đã được xác nhận',
    icon: <CheckCircleOutlined />,
    status: ['Đã xác nhận', 'CONFIRMED', 'confirmed']
  },
  {
    title: 'Đang chuẩn bị',
    description: 'Đang đóng gói sản phẩm',
    icon: <ShoppingOutlined />,
    status: ['Đang xử lý', 'PROCESSING', 'processing', 'Đang chuẩn bị']
  },
  {
    title: 'Đang giao hàng',
    description: 'Đơn hàng đang được vận chuyển',
    icon: <CarOutlined />,
    status: ['Đang giao', 'SHIPPING', 'shipping', 'Vận chuyển']
  },
  {
    title: 'Hoàn thành',
    description: 'Đơn hàng đã được giao thành công',
    icon: <HomeOutlined />,
    status: ['Đã giao', 'DELIVERED', 'delivered', 'Hoàn thành', 'COMPLETED', 'completed']
  }
];

// Interfaces
interface Order {
  id: string;
  user_id: string;
  status_id: string;
  total_price: string;
  shipping_name: string;
  shipping_phone: string;
  shipping_address: string;
  note: string;
  created_at: string;
  updated_at: string;
}

interface OrderItem {
  id: string;
  product_name: string;
  sku: string;
  image_url: string;
  quantity: number;
  price: number;
  total_price: number;
  unit_price?: number;
  name?: string;
  thumbnail_url?: string;
  version_id?: string;
  product?: any;
  version?: any;
}

interface OrderDetail extends Order {
  order_items?: OrderItem[];
  orderItems?: OrderItem[];
  items?: OrderItem[];
  payment_method?: 'COD' | 'BANK_TRANSFER' | 'TAP_WALLET';
  payment_status?: 'UNPAID' | 'PAID' | 'REFUNDED';
  sub_total?: number;
  shipping_fee?: number;
  discount?: number;
  status?: any;
}

interface CancelRequest { 
  id: string; 
  order: Order;
  reason: string; 
  requestedAt: string;
}

const OrderManagement: React.FC = () => {
  // State quản lý UI
  const [selectedOrder, setSelectedOrder] = useState<OrderDetail | null>(null);
  const [detailVisible, setDetailVisible] = useState(false);
  const [updateModalVisible, setUpdateModalVisible] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [searchText, setSearchText] = useState('');
  const [viewMode, setViewMode] = useState<'table' | 'card'>('table');
  const [filters, setFilters] = useState({
    orderStatus: '',
    paymentStatus: '',
    paymentMethod: '',
    dateRange: null as any
  });
  const [filterDrawerVisible, setFilterDrawerVisible] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // State cho các tham số API
  const debouncedSearchText = useDebounce(searchText, 500);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });

  // Form instance cho modal cập nhật
  const [form] = Form.useForm();

  // Gọi các custom hooks để tương tác với API
  const updateOrderStatusMutation = useUpdateOrderStatusHook();

  // Tạo tham số truy vấn cho API từ state của component
  const apiParams = useMemo(() => ({
    page: pagination.current,
    size: pagination.pageSize,
    search: debouncedSearchText,
    status_id: filters.orderStatus,
  }), [pagination, debouncedSearchText, filters]);
  
  // Lấy dữ liệu danh sách đơn hàng
  const { data: ordersData, error, isLoading, refetch } = useGetAdminOrdersHook(apiParams);

  // Xử lý dữ liệu trả về từ API
  const orders: Order[] = ordersData?.data || [];
  const totalOrders = ordersData?.data?.total ?? orders.length;

  // Sử dụng hook từ user order
  const [viewingOrderId, setViewingOrderId] = useState<string | null>(null);
  const { 
    data: orderDetailData, 
    isLoading: isDetailLoading, 
    error: detailError 
  } = useGetOrderByIdHook(viewingOrderId || '');

  // Lấy danh sách yêu cầu hủy
  const cancelParams = useMemo(() => ({ page: 1, size: 10, search: '' }), []);
  const { data: cancelRequestsData, isLoading: isCancelRequestsLoading, refetch: refetchCancelRequests } = useGetCancelRequestsHook(cancelParams);
  const cancelRequests: CancelRequest[] = cancelRequestsData?.data || [];

  const acceptCancelRequest = useAcceptCancelRequestHook();

  const {
    data: statusListData,
    isLoading: isStatusLoading,
    error: statusError,
  } = useGetStatusesByTypeHook({ type: 'ORDER', parent: null });
  const orderStatuses = statusListData || [];

  // Check if mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Xử lý lỗi từ API
  useEffect(() => {
    if (error) {
      message.error('Lỗi khi tải danh sách đơn hàng!');
    }
  }, [error]);

  // Cấu hình hiển thị cho trạng thái
  const getStatusInfo = (statusId: string) => {
    return orderStatuses.find((s: any) => s.id === statusId);
  };

  const paymentStatusConfig = {
    UNPAID: { color: 'red', text: 'Chưa thanh toán' },
    PAID: { color: 'green', text: 'Đã thanh toán' },
    REFUNDED: { color: 'orange', text: 'Đã hoàn tiền' }
  };

  const paymentMethodConfig = {
    COD: { text: 'COD', color: 'orange' },
    BANK_TRANSFER: { text: 'Chuyển khoản', color: 'blue' },
    TAP_WALLET: { text: 'Ví TAP', color: 'purple' }
  };

  // ✅ THÊM: Hàm xác định current step và status
  const getOrderProgress = (order: any) => {
    const currentStatus = order.status?.label || getStatusInfo(order.status_id)?.label || '';
    
    // Tìm step hiện tại
    let currentStep = 0;
    let stepStatus: 'wait' | 'process' | 'finish' | 'error' = 'process';
    
    // Xử lý trạng thái đã hủy
    if (currentStatus.toLowerCase().includes('hủy') || 
        currentStatus.toLowerCase().includes('cancel') ||
        currentStatus.toLowerCase().includes('đã hủy')) {
      return {
        currentStep: 0,
        stepStatus: 'error' as const,
        steps: ORDER_STEPS.map((step, index) => ({
          ...step,
          status: index === 0 ? ('error' as const) : ('wait' as const)
        }))
      };
    }
    
    // Tìm step tương ứng với trạng thái hiện tại
    for (let i = 0; i < ORDER_STEPS.length; i++) {
      const step = ORDER_STEPS[i];
      const isCurrentStep = step.status.some(status => 
        currentStatus.toLowerCase().includes(status.toLowerCase()) ||
        status.toLowerCase().includes(currentStatus.toLowerCase())
      );
      
      if (isCurrentStep) {
        currentStep = i;
        stepStatus = 'process';
        break;
      }
    }
    
    // Tạo steps với status phù hợp
    const steps = ORDER_STEPS.map((step, index) => ({
      ...step,
      status: index < currentStep ? 'finish' : (index === currentStep ? stepStatus : 'wait')
    }));
    
    return { currentStep, stepStatus, steps };
  };

  const viewOrderDetail = (order: Order) => {
    setViewingOrderId(order.id);
    setDetailVisible(true);
  };

  const openUpdateModal = (order: Order) => {
    setSelectedOrder(order as OrderDetail);
    form.setFieldsValue({ status_id: order.status_id });
    setUpdateModalVisible(true);
  };

  const handleUpdateStatus = async (values: UpdateOrderStatusRequest) => {
    if (!selectedOrder) return;
    const success = await updateOrderStatusMutation(selectedOrder.id, values);
    if (success) {
      setUpdateModalVisible(false);
      refetch();
    }
  };

  const handleTableChange = (paginationConfig: any) => {
    setPagination({
      current: paginationConfig.current,
      pageSize: paginationConfig.pageSize,
    });
  };

  const handleBulkAction = (action: string) => {
    if (selectedRowKeys.length === 0) {
      message.warning('Vui lòng chọn ít nhất một đơn hàng');
      return;
    }
    
    switch (action) {
      case 'print':
        message.success(`In ${selectedRowKeys.length} đơn hàng`);
        break;
      case 'confirm':
        message.success(`Xác nhận ${selectedRowKeys.length} đơn hàng`);
        break;
      case 'export':
        message.success(`Xuất Excel ${selectedRowKeys.length} đơn hàng`);
        break;
      case 'notify':
        message.success(`Gửi thông báo ${selectedRowKeys.length} khách hàng`);
        break;
    }
    setSelectedRowKeys([]);
  };

  const handleAcceptCancel = async (id: string) => {
    const success = await acceptCancelRequest(id);
    if (success) {
      refetchCancelRequests();
      refetch();
    }
  };

  // Desktop columns
  const desktopColumns = [
    {
      title: 'Mã đơn hàng',
      dataIndex: 'id',
      key: 'id',
      render: (id: string, record: Order) => (
        <Button
          type="link"
          onClick={() => viewOrderDetail(record)}
          className={styles.orderCodeButton}
        >
          {id.substring(0, 8)}...
        </Button>
      )
    },
    {
      title: 'Ngày đặt',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => (
        <div className={styles.dateText}>
          {text ? new Date(text).toLocaleString('vi-VN') : ''}
        </div>
      )
    },
    {
      title: 'Khách hàng',
      key: 'customer',
      render: (_: any, record: Order) => (
        <div className={styles.customerInfo}>
          <div className={styles.customerName}>{record.shipping_name}</div>
          <div className={styles.customerPhone}>{record.shipping_phone}</div>
        </div>
      )
    },
    {
      title: 'Sản phẩm',
      key: 'products',
      render: (_: any, record: Order) => (
        <div className={styles.productsList}>
          <Avatar.Group maxCount={3}>
            <Avatar size={32} className={styles.productAvatar}>
              {record.shipping_name?.charAt(0) || 'P'}
            </Avatar>
          </Avatar.Group>
          <span className={styles.itemCount}>
            Xem chi tiết
          </span>
        </div>
      )
    },
    {
      title: 'Tổng tiền',
      dataIndex: 'total_price',
      key: 'total_price',
      render: (total: string) => (
        <div className={styles.totalAmount}>
          {total ? parseFloat(total).toLocaleString() : '0'}₫
        </div>
      )
    },
    {
      title: 'Thanh toán',
      key: 'payment',
      render: (_: any, record: Order) => (
        <div className={styles.paymentInfo}>
          <Tag color="orange">COD</Tag>
          <Tag color="red">Chưa thanh toán</Tag>
        </div>
      )
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status_id',
      key: 'status_id',
      render: (statusId: string) => {
        const status = getStatusInfo(statusId);
        return status ? (
          <Tag color={status.color || 'default'}>
            {status.label}
          </Tag>
        ) : (
          <Tag>Không xác định</Tag>
        );
      }
    },
    {
      title: 'Hành động',
      key: 'actions',
      render: (_: any, record: Order) => (
        <Space size="small">
          <Tooltip title="Xem chi tiết">
            <Button
              variant="default"
              icon={<EyeOutlined />}
              onClick={() => viewOrderDetail(record)}
              size="small"
            >
              Xem
            </Button>
          </Tooltip>
          <Tooltip title="Cập nhật">
            <Button
              variant="primary"
              icon={<EditOutlined />}
              onClick={() => openUpdateModal(record)}
              size="small"
            >
              Sửa
            </Button>
          </Tooltip>
          <Tooltip title="In đơn">
            <Button
              variant="default"
              icon={<PrinterOutlined />}
              onClick={() => message.info(`In đơn ${record.id}`)}
              size="small"
            >
              In
            </Button>
          </Tooltip>
          <Tooltip title="Liên hệ">
            <Button
              variant="default"
              icon={<MessageOutlined />}
              onClick={() => message.info(`Liên hệ ${record.shipping_name}`)}
              size="small"
            >
              Chat
            </Button>
          </Tooltip>
        </Space>
      )
    }
  ];

  // Mobile columns
  const mobileColumns = [
    {
      title: 'Đơn hàng',
      key: 'order',
      render: (_: any, record: Order) => (
        <div className={styles.mobileOrderCard}>
          <div className={styles.mobileOrderHeader}>
            <div className={styles.orderCodeMobile}>
              <Button
                type="link"
                onClick={() => viewOrderDetail(record)}
                className={styles.orderCodeButtonMobile}
              >
                {record.id.substring(0, 8)}...
              </Button>
              <span className={styles.orderDateMobile}>{record.created_at ? new Date(record.created_at).toLocaleString('vi-VN') : ''}</span>
            </div>
            <div className={styles.orderStatusMobile}>
              <Tag 
                color={getStatusInfo(record.status_id)?.color || 'default'}
              >
                {getStatusInfo(record.status_id)?.label || 'Không xác định'}
              </Tag>
            </div>
          </div>
          
          <div className={styles.mobileOrderBody}>
            <div className={styles.customerInfoMobile}>
              <UserOutlined className={styles.customerIcon} />
              <div>
                <div className={styles.customerNameMobile}>{record.shipping_name}</div>
                <div className={styles.customerPhoneMobile}>{record.shipping_phone}</div>
              </div>
            </div>
            
            <div className={styles.productInfoMobile}>
              <Avatar.Group maxCount={3} size="small">
                <Avatar className={styles.productAvatarMobile}>
                  {record.shipping_name?.charAt(0) || 'P'}
                </Avatar>
              </Avatar.Group>
              <span className={styles.itemCountMobile}>
                Xem chi tiết
              </span>
            </div>
          </div>
          
          <div className={styles.mobileOrderFooter}>
            <div className={styles.paymentInfoMobile}>
              <Tag color="orange">COD</Tag>
              <Tag color="red">Chưa thanh toán</Tag>
            </div>
            <div className={styles.totalAmountMobile}>
              {record.total_price ? parseFloat(record.total_price).toLocaleString() : '0'}₫
            </div>
          </div>
          
          <div className={styles.mobileOrderActions}>
            <Button
              variant="default"
              icon={<EyeOutlined />}
              onClick={() => viewOrderDetail(record)}
              size="small"
            >
              Xem
            </Button>
            <Button
              variant="primary"
              icon={<EditOutlined />}
              onClick={() => openUpdateModal(record)}
              size="small"
            >
              Sửa
            </Button>
            <Button
              variant="default"
              icon={<PrinterOutlined />}
              onClick={() => message.info(`In đơn ${record.id}`)}
              size="small"
            >
              In
            </Button>
            <Button
              variant="default"
              icon={<MessageOutlined />}
              onClick={() => message.info(`Liên hệ ${record.shipping_name}`)}
              size="small"
            >
              Chat
            </Button>
          </div>
        </div>
      )
    }
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
  };

  const bulkActions = [
    { key: 'print', label: 'In phiếu giao', icon: <PrinterOutlined /> },
    { key: 'confirm', label: 'Xác nhận đơn', icon: <CheckCircleOutlined /> },
    { key: 'export', label: 'Xuất Excel', icon: <ExportOutlined /> },
    { key: 'notify', label: 'Gửi thông báo', icon: <BellOutlined /> }
  ];

  // Quick stats
  const todayOrders = orders.filter(order => 
    order.created_at && new Date(order.created_at).toDateString() === new Date().toDateString()
  ).length;
  const processingOrders = orders.filter(order => 
    order.status_id === 'c3075676-5059-4363-b2c7-97b8968f2fb7'
  ).length;
  const completedOrders = orders.filter(order => 
    order.status_id === 'dd99d77f-3795-4bb8-91aa-df78a9081d4c'
  ).length;
  const cancelledOrders = orders.filter(order => 
    ['cancelled'].includes(order.status_id)
  ).length;

  const renderMobileStats = () => (
    <div className={styles.mobileStatsContainer}>
      <Collapse ghost>
        <Panel header="Thống kê nhanh" key="1">
          <Row gutter={[8, 8]}>
            <Col span={12}>
              <div className={styles.mobileStatCard}>
                <div className={styles.mobileStatValue}>{todayOrders}</div>
                <div className={styles.mobileStatLabel}>Đơn hôm nay</div>
              </div>
            </Col>
            <Col span={12}>
              <div className={styles.mobileStatCard}>
                <div className={styles.mobileStatValue}>{processingOrders}</div>
                <div className={styles.mobileStatLabel}>Đang xử lý</div>
              </div>
            </Col>
            <Col span={12}>
              <div className={styles.mobileStatCard}>
                <div className={styles.mobileStatValue}>{completedOrders}</div>
                <div className={styles.mobileStatLabel}>Hoàn thành</div>
              </div>
            </Col>
            <Col span={12}>
              <div className={styles.mobileStatCard}>
                <div className={styles.mobileStatValue}>{cancelledOrders}</div>
                <div className={styles.mobileStatLabel}>Đã hủy</div>
              </div>
            </Col>
          </Row>
        </Panel>
      </Collapse>
    </div>
  );

  // ✅ THÊM: Cập nhật renderOrderDetail với tiến trình thực tế
  const renderOrderDetail = () => {
    if (isDetailLoading) {
      return (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
          <p>Đang tải thông tin chi tiết...</p>
        </div>
      );
    }

    if (detailError) {
      return (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Alert
            message="Lỗi"
            description="Không thể tải chi tiết đơn hàng. Vui lòng thử lại."
            type="error"
            showIcon
          />
        </div>
      );
    }

    const order = orderDetailData?.data || orderDetailData;
    
    if (!order) {
      return (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Alert
            message="Không có dữ liệu"
            description="Không tìm thấy thông tin chi tiết đơn hàng."
            type="warning"
            showIcon
          />
        </div>
      );
    }

    // ✅ THÊM: Tính toán tiến trình
    const { currentStep, stepStatus, steps } = getOrderProgress(order);

    return (
      <div className={styles.orderDetailContainer}>
        <div className={styles.orderDetailHeader}>
          <h3>Chi tiết đơn hàng {order.id?.substring(0, 8)}...</h3>
          <Space>
            <Button variant="default" icon={<PrinterOutlined />}>
              In đơn
            </Button>
            <Button variant="primary" icon={<EditOutlined />}>
              Cập nhật
            </Button>
          </Space>
        </div>

        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card title="Thông tin chung" className={styles.detailCard}>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="Mã đơn hàng">
                  {order.id}
                </Descriptions.Item>
                <Descriptions.Item label="Ngày đặt">
                  {order.created_at ? new Date(order.created_at).toLocaleString('vi-VN') : 'Không có'}
                </Descriptions.Item>
                <Descriptions.Item label="Trạng thái đơn">
                  <Tag color={order.status?.color || getStatusInfo(order.status_id)?.color || 'default'}>
                    {order.status?.label || getStatusInfo(order.status_id)?.label || 'Không xác định'}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="Trạng thái thanh toán">
                  <Tag color={paymentStatusConfig[order.payment_status as keyof typeof paymentStatusConfig]?.color || 'red'}>
                    {paymentStatusConfig[order.payment_status as keyof typeof paymentStatusConfig]?.text || 'Chưa thanh toán'}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="Ghi chú khách hàng">
                  {order.note || 'Không có'}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>

          <Col xs={24} lg={12}>
            <Card title="Thông tin khách hàng" className={styles.detailCard}>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="Họ tên">
                  {order.shipping_name || 'Không có'}
                </Descriptions.Item>
                <Descriptions.Item label="Số điện thoại">
                  {order.shipping_phone || 'Không có'}
                </Descriptions.Item>
                <Descriptions.Item label="Địa chỉ">
                  {order.shipping_address || 'Không có'}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>

          {(order.order_items || order.orderItems || order.items) && (
            <Col xs={24}>
              <Card title="Danh sách sản phẩm" className={styles.detailCard}>
                <Table
                  dataSource={order.order_items || order.orderItems || order.items}
                  rowKey="id"
                  pagination={false}
                  size="small"
                  columns={[
                    {
                      title: 'Sản phẩm',
                      key: 'product',
                      render: (_, item: any) => (
                        <div className={styles.productDetail}>
                          <Avatar 
                            src={item.image_url || item.thumbnail_url || item.product?.thumbnail_url || logo} 
                            size={40} 
                          />
                          <div className={styles.productInfo}>
                            <div className={styles.productName}>
                              {item.product_name || item.name || item.product?.name || 'Không có tên'}
                            </div>
                            <div className={styles.productSku}>
                              SKU: {item.sku || item.version_id || item.version?.name || 'N/A'}
                            </div>
                          </div>
                        </div>
                      )
                    },
                    {
                      title: 'Số lượng',
                      dataIndex: 'quantity',
                      key: 'quantity',
                      render: (qty) => qty || 0
                    },
                    {
                      title: 'Đơn giá',
                      key: 'price',
                      render: (_, item: any) => {
                        const price = item.price || item.unit_price || 0;
                        return `${Number(price).toLocaleString()}₫`;
                      }
                    },
                    {
                      title: 'Thành tiền',
                      key: 'total_price',
                      render: (_, item: any) => {
                        const total = item.total_price || (item.quantity * (item.price || item.unit_price)) || 0;
                        return `${Number(total).toLocaleString()}₫`;
                      }
                    }
                  ]}
                />
              </Card>
            </Col>
          )}

          <Col xs={24} lg={12}>
            <Card title="Tài chính" className={styles.detailCard}>
              <div className={styles.financialInfo}>
                <div className={styles.financialRow}>
                  <span>Tổng giá trị:</span>
                  <span>{Number(order.sub_total || order.total_price || 0).toLocaleString()}₫</span>
                </div>
                <div className={styles.financialRow}>
                  <span>Phí vận chuyển:</span>
                  <span>{Number(order.shipping_fee || 0).toLocaleString()}₫</span>
                </div>
                <div className={styles.financialRow}>
                  <span>Giảm giá:</span>
                  <span>-{Number(order.discount || 0).toLocaleString()}₫</span>
                </div>
                <Divider />
                <div className={styles.financialRow}>
                  <strong>Tổng thanh toán:</strong>
                  <strong className={styles.totalFinal}>
                    {Number(order.total_price || 0).toLocaleString()}₫
                  </strong>
                </div>
              </div>
            </Card>
          </Col>

          <Col xs={24} lg={12}>
            <Card title="Tiến trình xử lý" className={styles.detailCard}>
              <Steps
                direction="vertical"
                size="small"
                current={currentStep}
                status={stepStatus}
              >
                {steps.map((step, index) => (
                  <Step
                    key={index}
                    title={step.title}
                    description={step.description}
                    icon={step.icon}
                    status={step.status as any}
                  />
                ))}
              </Steps>
              
              {/* ✅ THÊM: Thông tin trạng thái hiện tại */}
              <div style={{ marginTop: '16px', padding: '12px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
                <strong>Trạng thái hiện tại:</strong> {order.status?.label || getStatusInfo(order.status_id)?.label || 'Không xác định'}
                <br />
                <strong>Cập nhật lần cuối:</strong> {order.updated_at ? new Date(order.updated_at).toLocaleString('vi-VN') : 'Không có'}
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <div className={styles.headerTitle}>
          <h1>Quản lý đơn hàng</h1>
          {!isMobile && <p>Quản lý toàn bộ đơn hàng của cửa hàng</p>}
        </div>
        <div className={styles.headerActions}>
          <Badge count={processingOrders} showZero>
            <Button
              icon={<BellOutlined />}
              className={styles.notificationButton}
            >
              Thông báo
            </Button>
          </Badge>
          <Button
            type="primary"
            icon={<EyeOutlined />}
            className={styles.quickAccessButton}
          >
            Đơn mới ({todayOrders})
          </Button>
        </div>
      </div>

      {/* Search Bar - Mobile */}
      {isMobile && (
        <div className={styles.mobileSearchContainer}>
          <Input
            placeholder="Tìm mã đơn, SĐT, tên khách..."
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            className={styles.mobileSearchInput}
          />
          <Button
            icon={<FilterOutlined />}
            onClick={() => setFilterDrawerVisible(true)}
            className={styles.mobileFilterButton}
          >
            Lọc
          </Button>
        </div>
      )}

      {/* Stats - Desktop */}
      {!isMobile && (
        <Row gutter={16} className={styles.statsRow}>
          <Col xs={24} sm={12} lg={6}>
            <Card className={styles.statCard}>
              <Statistic
                title="Đơn hôm nay"
                value={todayOrders}
                prefix={<CalendarOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card className={styles.statCard}>
              <Statistic
                title="Đang xử lý"
                value={processingOrders}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card className={styles.statCard}>
              <Statistic
                title="Hoàn thành"
                value={completedOrders}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card className={styles.statCard}>
              <Statistic
                title="Đã hủy"
                value={cancelledOrders}
                prefix={<CloseCircleOutlined />}
                valueStyle={{ color: '#ff4d4f' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* Stats - Mobile */}
      {isMobile && renderMobileStats()}

      {/* Filters & View Toggle */}
      <Card className={styles.filterCard}>
        <div className={styles.filterHeader}>
          <div className={styles.filterLeft}>
            {!isMobile && (
              <Input
                placeholder="Tìm mã đơn, SĐT, tên khách..."
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                className={styles.searchInput}
                style={{ width: 280 }}
              />
            )}
          </div>
          <div className={styles.filterRight}>
            <Radio.Group
              value={viewMode}
              onChange={(e) => setViewMode(e.target.value)}
              buttonStyle="solid"
              className={styles.viewToggle}
            >
              <Radio.Button value="table">
                <UnorderedListOutlined /> Bảng
              </Radio.Button>
              <Radio.Button value="card">
                <AppstoreOutlined /> Thẻ
              </Radio.Button>
            </Radio.Group>
          </div>
        </div>

        {!isMobile && (
          <Row gutter={16} className={styles.filterRow}>
            <Col xs={24} sm={12} md={6}>
              <Select
                placeholder="Trạng thái đơn hàng"
                value={filters.orderStatus}
                onChange={(value) => setFilters({...filters, orderStatus: value})}
                className={styles.filterSelect}
                allowClear
              >
                {orderStatuses.map(status => (
                  <Option key={status.id} value={status.id}>{status.label}</Option>
                ))}
              </Select>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Select
                placeholder="Trạng thái thanh toán"
                value={filters.paymentStatus}
                onChange={(value) => setFilters({...filters, paymentStatus: value})}
                className={styles.filterSelect}
                allowClear
              >
                <Option value="unpaid">Chưa thanh toán</Option>
                <Option value="paid">Đã thanh toán</Option>
                <Option value="refunded">Đã hoàn tiền</Option>
              </Select>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Select
                placeholder="Phương thức thanh toán"
                value={filters.paymentMethod}
                onChange={(value) => setFilters({...filters, paymentMethod: value})}
                className={styles.filterSelect}
                allowClear
              >
                <Option value="cod">COD</Option>
                <Option value="bank_transfer">Chuyển khoản</Option>
                <Option value="tap_wallet">Ví TAP</Option>
              </Select>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <RangePicker
                placeholder={['Từ ngày', 'Đến ngày']}
                className={styles.dateRangePicker}
                onChange={(dates) => setFilters({...filters, dateRange: dates})}
              />
            </Col>
          </Row>
        )}
      </Card>

      {/* Bulk Actions */}
      {selectedRowKeys.length > 0 && (
        <Card className={styles.bulkCard}>
          <div className={styles.bulkActions}>
            <span>Đã chọn {selectedRowKeys.length} đơn hàng</span>
            <Space>
              {bulkActions.map(action => (
                <Button
                  key={action.key}
                  icon={action.icon}
                  onClick={() => handleBulkAction(action.key)}
                  size={isMobile ? 'small' : 'middle'}
                >
                  {action.label}
                </Button>
              ))}
            </Space>
          </div>
        </Card>
      )}

      {/* Orders Table */}
      <Card className={styles.tableCard}>
        <Table
          columns={isMobile ? mobileColumns : desktopColumns}
          dataSource={orders}
          rowKey="id"
          loading={isLoading}
          rowSelection={rowSelection}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: totalOrders,
            showSizeChanger: !isMobile,
            showQuickJumper: !isMobile,
            showTotal: (total, range) => 
              isMobile ? `${range[0]}-${range[1]}/${total}` : `${range[0]}-${range[1]} của ${total} đơn hàng`,
            size: isMobile ? 'small' : 'default'
          }}
          onChange={handleTableChange}
          className={styles.orderTable}
          scroll={isMobile ? { x: 'max-content' } : undefined}
        />
      </Card>

      {/* Filter Drawer - Mobile */}
      <Drawer
        title="Bộ lọc"
        placement="bottom"
        onClose={() => setFilterDrawerVisible(false)}
        open={filterDrawerVisible}
        height="auto"
        className={styles.filterDrawer}
      >
        <div className={styles.filtersContainer}>
          <div className={styles.mobileFilterItem}>
            <label>Trạng thái đơn hàng:</label>
            <Select
              placeholder="Chọn trạng thái"
              value={filters.orderStatus}
              onChange={(value) => setFilters({...filters, orderStatus: value})}
              className={styles.filterSelect}
              allowClear
            >
              {orderStatuses.map(status => (
                <Option key={status.id} value={status.id}>{status.label}</Option>
              ))}
            </Select>
          </div>
          <div className={styles.mobileFilterItem}>
            <label>Trạng thái thanh toán:</label>
            <Select
              placeholder="Chọn trạng thái"
              value={filters.paymentStatus}
              onChange={(value) => setFilters({...filters, paymentStatus: value})}
              className={styles.filterSelect}
              allowClear
            >
              <Option value="unpaid">Chưa thanh toán</Option>
              <Option value="paid">Đã thanh toán</Option>
              <Option value="refunded">Đã hoàn tiền</Option>
            </Select>
          </div>
          <div className={styles.mobileFilterItem}>
            <label>Phương thức thanh toán:</label>
            <Select
              placeholder="Chọn phương thức"
              value={filters.paymentMethod}
              onChange={(value) => setFilters({...filters, paymentMethod: value})}
              className={styles.filterSelect}
              allowClear
            >
              <Option value="cod">COD</Option>
              <Option value="bank_transfer">Chuyển khoản</Option>
              <Option value="tap_wallet">Ví TAP</Option>
            </Select>
          </div>
        </div>
        <div className={styles.drawerActions}>
          <Button onClick={() => setFilterDrawerVisible(false)}>
            Đóng
          </Button>
          <Button 
            type="primary" 
            onClick={() => {
              setFilterDrawerVisible(false);
              message.success('Áp dụng bộ lọc');
            }}
          >
            Áp dụng
          </Button>
        </div>
      </Drawer>

      {/* Order Detail Modal */}
      <Modal
        title="Chi tiết đơn hàng"
        open={detailVisible}
        onCancel={() => {
          setDetailVisible(false);
          setViewingOrderId(null);
        }}
        footer={null}
        width={isMobile ? '100%' : 1000}
        style={isMobile ? { top: 0, paddingBottom: 0 } : undefined}
        className={styles.orderDetailModal}
        destroyOnClose={true}
      >
        {renderOrderDetail()}
      </Modal>

      {/* Update Status Modal */}
      <Modal
        title="Cập nhật trạng thái đơn hàng"
        open={updateModalVisible}
        onCancel={() => setUpdateModalVisible(false)}
        footer={null}
      >
        <Form form={form} layout="vertical" onFinish={handleUpdateStatus}>
          <p>Đơn hàng: <strong>{selectedOrder?.id.substring(0,8)}...</strong></p>
          <Form.Item name="status_id" label="Chọn trạng thái mới" rules={[{ required: true, message: 'Vui lòng chọn trạng thái!' }]}>
            <Select placeholder="Chọn trạng thái">
              {orderStatuses.map(status => (
                <Option key={status.id} value={status.id}>{status.label}</Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
        
        <ModalButtonGroup
          onCancel={() => setUpdateModalVisible(false)}
          onConfirm={() => form.submit()}
          cancelText="Hủy"
          confirmText="OK"
          confirmLoading={isLoading}
        />
      </Modal>
    </div>
  );
};

export default OrderManagement;
