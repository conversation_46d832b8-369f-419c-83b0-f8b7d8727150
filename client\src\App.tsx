import React, { Component } from 'react'
import ChatBotWidget from './components/ChatBot/ChatBotWidget'
import { Routes, Route } from 'react-router-dom'
import PublicLayout from './layouts/PublicLayout'
import ProtectedLayout from './layouts/ProtectedLayout'
import SystemLayout from './layouts/SystemLayout'
import HomePageLayout from './layouts/HomePageLayout'
import NotFound from './pages/Error/Notfound'
import type { ReactNode } from 'react'
import { publicRoutes, privateRoutes } from './routes'
import { PermissionGuard } from './helpers/ProtectedRoutes'

class ErrorBoundary extends Component<{ children: ReactNode }, { hasError: boolean }> {
  state = { hasError: false }

  static getDerivedStateFromError() {
    return { hasError: true }
  }

  render() {
    if (this.state.hasError) {
      return <h1>Đã xảy ra lỗi. Vui lòng thử lại.</h1>
    }
    return this.props.children
  }
}

function App() {
  return (
    <ErrorBoundary>
      <Routes>
        {/* Public layout routes */}
        <Route element={<PublicLayout />}>
          {publicRoutes.map(({ path, component: Page, layout: Layout = React.Fragment }, index) => (
            <Route
              key={index}
              path={path}
              element={
                <Layout>
                  <Page />
                </Layout>
              }
            />
          ))}
        </Route>

        {/* Protected layout routes */}
        <Route element={<ProtectedLayout />}>
          {privateRoutes
            .filter(route => route.layout === SystemLayout)
            .map(({ path, component: Page, requiredPermissions }, index) => (
            <Route
              key={index}
              path={path}
              element={
                <PermissionGuard requiredPermissions={requiredPermissions}>
                  <Page />
                </PermissionGuard>
              }
            />
          ))}
        </Route>

        {/* HomePage layout routes */}
        {privateRoutes
          .filter(route => route.layout === HomePageLayout)
          .map(({ path, component: Page, requiredPermissions }, index) => (
            <Route
              key={index}
              path={path}
              element={
                <PermissionGuard requiredPermissions={requiredPermissions}>
                  <HomePageLayout>
                    <Page />
                  </HomePageLayout>
                </PermissionGuard>
              }
            />
          ))}

        {/* Fallback */}
        <Route path="*" element={<NotFound />} />
      </Routes>
      <ChatBotWidget />
    </ErrorBoundary>
  )
}

export default App
