import { createApi } from '@reduxjs/toolkit/query/react';
import { axiosBaseQuery } from '../../../libs/axios/axiosBase';

const baseUrl = 'http://localhost:8080';

// Types cho request/response
export interface OrderItem {
  product_id: string;
  version_id: string;
  quantity: number;
  unit_price: number;
  selected_options: Record<string, any>;
}

export interface CreateOrderRequest {
  source: 'cart' | 'buy_now';
  items: OrderItem[];
  shipping_address: string;
  receiver_name: string;
  receiver_phone: string;
  note?: string;
  guest_phone?: string;
  guest_email?: string;
  ref_code?: string;
  is_guest?: boolean;
}

export interface OrderResponse {
  data?: any;
  message?: string;
  statusCode?: number;
}

export const orderApi = createApi({
  reducerPath: 'orderApi',
  baseQuery: axiosBaseQuery({ baseUrl }),
  // ✅ 1. Thêm 'Cart' vào tagTypes để orderApi có thể làm mới dữ liệu giỏ hàng
  tagTypes: ['Orders', 'Cart'],
  endpoints: (build) => ({
    // L<PERSON>y danh sách đơn hàng của user hiện tại
    getMyOrders: build.query<OrderResponse, void>({
      query: () => ({
        url: '/orders/me',
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['Orders'],
    }),
    // Lấy chi tiết đơn hàng theo id
    getOrderById: build.query<OrderResponse, string>({
      query: (id: string) => ({
        url: `/orders/${id}`,
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['Orders'],
    }),
    // Tạo đơn hàng mới
    createOrder: build.mutation<OrderResponse, CreateOrderRequest>({
      query: (data: CreateOrderRequest) => ({
        url: '/orders',
        method: 'POST',
        data: data,
        authRequired: true,
      }),
      // ✅ 2. Thêm 'Cart' vào đây. Khi đơn hàng tạo thành công, 
      // nó sẽ tự động refetch lại giỏ hàng.
      invalidatesTags: ['Orders', 'Cart'],
    }),
    // Yêu cầu hủy đơn hàng
    requestCancelOrder: build.mutation<OrderResponse, string>({
      query: (id: string) => ({
        url: `/orders/${id}/request-cancel`,
        method: 'POST',
        authRequired: true,
      }),
      invalidatesTags: ['Orders'],
    }),
    // Xác nhận đã nhận hàng
    confirmReceivedOrder: build.mutation<OrderResponse, string>({
      query: (id: string) => ({
        url: `/orders/${id}/confirm-received`,
        method: 'POST',
        authRequired: true,
      }),
      invalidatesTags: ['Orders'],
    }),
  }),
});

export const {
  useGetMyOrdersQuery,
  useGetOrderByIdQuery,
  useCreateOrderMutation,
  useRequestCancelOrderMutation,
  useConfirmReceivedOrderMutation,
} = orderApi;
