import axios from 'axios';
import {
  Injectable,
  ConflictException,
  NotFoundException,
  InternalServerErrorException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, IsNull } from 'typeorm';
import { Product } from '../entities/products.entity';
import { ProductQrCode, ProductQrCodeType } from '../entities/product_qr_code.entity';
import { ConfigService } from '@nestjs/config';
import { ProductAttribute } from '../entities/product_attribute.entity';
import { CreateProductDto } from '../dto/create-product.dto';
import { ProductImage } from '../entities/product_image.entity';
import slugify from 'slugify';
import { Inject, forwardRef } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { UpdateProductAttributesDto } from '../dto/update-product-attributes.dto';
import { ProductVersion } from '../entities/product_version.entity';
import { ProductVersionDto } from '../dto/product-version.dto';
import { UpdateProductDto } from '../dto/update-product.dto';
import { ProductListRequestDto } from '../dto/product-list-request.dto';
import { ProductCategoriesService } from './product-categories.service';

@Injectable()
export class ProductService {
  constructor(
    @InjectRepository(Product)
    private readonly productRepo: Repository<Product>,

    @InjectRepository(ProductImage)
    private readonly imageRepo: Repository<ProductImage>,

    @Inject('LOG_SERVICE') // tên provider RabbitMQ
    private readonly logClient: ClientProxy,

    @Inject('IMAGE_VECTOR_SERVICE') private imageVectorClient: ClientProxy,

    @InjectRepository(ProductAttribute)
    private readonly attributeRepo: Repository<ProductAttribute>,

    @InjectRepository(ProductVersion)
    private readonly versionRepo: Repository<ProductVersion>,

    private readonly productCategoriesService: ProductCategoriesService,

    @InjectRepository(ProductQrCode)
    private readonly qrCodeRepo: Repository<ProductQrCode>,

    private readonly configService: ConfigService,
  ) {}

  /**
   * ➕ Tạo sản phẩm mới
   * @param dto Dữ liệu sản phẩm
   * @param userId UID người dùng tạo sản phẩm
   * @returns Sản phẩm đã lưu
   */
  async create(dto: CreateProductDto, userId: string): Promise<Product> {
    const queryRunner = this.productRepo.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      // Lấy thumbnail_url nếu có ảnh is_thumbnail
      let thumbnailUrl: string | undefined = undefined;
      if (dto.product_images && dto.product_images.length > 0) {
        const thumb = dto.product_images.find((img) => img.is_thumbnail);
        if (thumb && thumb.image_url) {
          thumbnailUrl = thumb.image_url;
        }
      }
      // Nếu không truyền status_id thì BE tự tìm status DRAFT
      let statusId = dto.status_id;
      if (!statusId) {
        const statusRepo = this.productRepo.manager.getRepository('Status');
        const draftStatus = await statusRepo.findOne({ where: { code: 'DRAFT', type: 'PRODUCT' } });
        if (!draftStatus)
          throw new NotFoundException('Không tìm thấy trạng thái DRAFT cho sản phẩm');
        statusId = draftStatus.id;
      }
      const product = this.productRepo.create({
        name: dto.name,
        slug: slugify(dto.name, { lower: true }),
        description: dto.description,
        price: dto.price,
        unit: dto.unit,
        category_id: dto.category_id,
        stock: 0,
        status_id: statusId,
        is_public: true,
        user_id: userId,
        thumbnail_url: thumbnailUrl,
      });
      let savedProduct;
      try {
        savedProduct = await queryRunner.manager.save(product);
      } catch (err) {
        if (err.code === '23505' && err.detail?.includes('unique')) {
          await queryRunner.rollbackTransaction();
          throw new ConflictException('Sản phẩm đã tồn tại vui lòng kiểm tra lại');
        }
        throw new BadRequestException('Tạo sản phẩm thất bại: ' + (err?.message || err));
      }

      // Gán product_id cho các bản ghi ProductImage tạm thời (đã upload, product_id=null)
      if (dto.product_images && dto.product_images.length > 0) {
        // Lấy status_id cho IMAGE/UPLOADING
        const statusRepo = this.productRepo.manager.getRepository('Status');
        const uploadedStatus = await statusRepo.findOne({
          where: { code: 'UPLOADING', type: 'IMAGE' },
        });
        if (!uploadedStatus) throw new BadRequestException('Không tìm thấy status IMAGE/UPLOADING');

        // Lấy danh sách image_id từ dto
        const imageIds = dto.product_images.map((img) => img.public_image_id);
        console.log('[ProductService][create] imageIds:', imageIds);

        // Lấy các bản ghi ProductImage tạm (product_id IS NULL, image_id in imageIds)
        const tempImages = await this.imageRepo.find({
          where: { product_id: IsNull(), image_id: In(imageIds) },
        });
        console.log('[ProductService][create] tempImages before update:', tempImages);

        // Map theo image_id để update đúng thứ tự, is_thumbnail
        for (let i = 0; i < dto.product_images.length; i++) {
          const imgDto = dto.product_images[i];
          const temp = tempImages.find((t) => t.image_id === imgDto.public_image_id);
          if (temp) {
            temp.product_id = savedProduct.id;
            temp.status_id = uploadedStatus.id;
            temp.display_order = i;
            temp.is_thumbnail = imgDto.is_thumbnail || false;
            console.log(`[ProductService][create] Update tempImage:`, temp);
          } else {
            console.warn(
              `[ProductService][create] Không tìm thấy tempImage cho image_id:`,
              imgDto.public_image_id,
            );
          }
        }
        await queryRunner.manager.save(tempImages);

        console.log('[ProductService][create] tempImages after update:', tempImages);
      }

      // Lưu attributes nếu có
      if (dto.product_attribute && Array.isArray(dto.product_attribute)) {
        const attrEntities = dto.product_attribute.map((attr) =>
          this.attributeRepo.create({
            product_id: savedProduct.id,
            name: attr.attribute_name,
            value: attr.value,
            unit: attr.unit,
            is_searchable: attr.is_searchable,
          }),
        );
        await queryRunner.manager.save(attrEntities);
      }

      // Lưu versions nếu có
      if (dto.product_versions && Array.isArray(dto.product_versions)) {
        const versionEntities = dto.product_versions.map((ver) =>
          this.versionRepo.create({
            product_id: savedProduct.id,
            name: ver.name,
            price: ver.price,
            stock: ver.stock,
            options: ver.options,
            sku_code: ver.sku_code,
          }),
        );
        const savedVersions = await queryRunner.manager.save(versionEntities);
        // Tạo tồn kho cho từng version
        await this._createInventoryForProductVersions(
          savedProduct.id,
          savedVersions,
          queryRunner.manager,
        );
      }

      await this.logClient.emit('log_queue', {
        action: 'product.create',
        user_id: userId,

        module: 'M2',
        description: `Tạo sản phẩm mới: "${savedProduct.name}"`,
        target_id: savedProduct.id,
        metadata: JSON.stringify({
          name: savedProduct.name,
          price: savedProduct.price,
          unit: savedProduct.unit,
          status: savedProduct.status,
        }),
      });

      await queryRunner.commitTransaction();

      // Sau khi tạo sản phẩm thành công, gửi message vector hóa cho từng ảnh
      if (dto.product_images && dto.product_images.length > 0) {
        for (const img of dto.product_images) {
          if (img.public_image_id && img.image_url) {
            this.imageVectorClient.emit('image-vectorize', {
              productId: savedProduct.id,
              imageId: img.public_image_id,
              imageUrl: img.image_url,
            });
          }
        }
      }
      return savedProduct;
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw new BadRequestException('Tạo sản phẩm thất bại: ' + (err?.message || err));
    } finally {
      await queryRunner.release();
    }
  }
  /**
   * Cập nhật toàn bộ sản phẩm
   */
  async update(id: string, dto: UpdateProductDto, userId: string): Promise<Product> {
    const queryRunner = this.productRepo.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      // 1. Update product info
      const product = await this.productRepo.findOne({ where: { id } });
      if (!product) throw new NotFoundException('Không tìm thấy sản phẩm');
      // Chỉ gán các trường thuộc bảng product
      if (dto.name) product.name = dto.name;
      if (dto.description !== undefined) product.description = dto.description;
      if (dto.price !== undefined) product.price = dto.price;
      if (dto.unit !== undefined) product.unit = dto.unit;
      if (dto.category_id !== undefined) product.category_id = dto.category_id;
      if (dto.unit !== undefined) product.unit = dto.unit;
      if (dto.status_id !== undefined) product.status_id = dto.status_id;
      if (dto.name) product.slug = slugify(dto.name, { lower: true });
      // các trường khác nếu cần update
      ///...
      const savedProduct = await queryRunner.manager.save(product);

      // 2. Update images (xử lý cả khi mảng rỗng để xóa hết ảnh)
      if (dto.product_images) {
        const oldImages = await this.imageRepo.find({ where: { product_id: id } });
        if (dto.product_images.length === 0) {
          // Nếu FE gửi mảng rỗng => xóa hết ảnh cũ
          if (oldImages.length > 0) {
            await queryRunner.manager.delete(ProductImage, { product_id: id });
          }
        } else {
          const oldImageIds = oldImages.map((img) => img.image_id);
          const newImageIds = dto.product_images.map((img) => img.public_image_id);
          // Xóa ảnh không còn trong danh sách mới
          const toDelete = oldImages.filter((img) => !newImageIds.includes(img.image_id));
          if (toDelete.length > 0) {
            await queryRunner.manager.delete(
              ProductImage,
              toDelete.map((i) => i.id),
            );
          }
          // Thêm ảnh mới chưa có
          const toAdd = dto.product_images.filter(
            (img) => !oldImageIds.includes(img.public_image_id),
          );
          if (toAdd.length > 0) {
            const newEntities = toAdd.map((img) =>
              this.imageRepo.create({
                product_id: id,
                image_url: img.image_url,
                image_id: img.public_image_id,
                display_order: 0, // sẽ cập nhật lại sau
              }),
            );
            await queryRunner.manager.save(newEntities);
          }
          // Cập nhật lại display_order cho tất cả ảnh (chỉ lấy các ảnh còn tồn tại và đúng thứ tự theo DTO)
          const allImages = await this.imageRepo.find({ where: { product_id: id } });
          const sorted = dto.product_images
            .map((img) => allImages.find((a) => a.image_id === img.public_image_id))
            .filter(Boolean);
          for (let i = 0; i < sorted.length; i++) {
            if (sorted[i]) sorted[i]!.display_order = i;
          }
          await queryRunner.manager.save(sorted);
        }
      }

      // 3. Update attributes (overwrite)
      if (dto.product_attribute && dto.product_attribute.length > 0) {
        await queryRunner.manager.delete(ProductAttribute, { product_id: id });
        const attrEntities = dto.product_attribute.map((attr) =>
          this.attributeRepo.create({
            product_id: id,
            name: attr.attribute_name,
            value: attr.value,
            unit: attr.unit,
            is_searchable: attr.is_searchable,
          }),
        );
        await queryRunner.manager.save(attrEntities);
      }

      // 4. Update product versions
      if (dto.product_versions && dto.product_versions.length > 0) {
        for (const ver of dto.product_versions) {
          if (ver.id) {
            // Update version theo id
            const version = await this.versionRepo.findOne({
              where: { id: ver.id, product_id: id },
            });
            if (version) {
              Object.assign(version, ver);
              await queryRunner.manager.save(version);
            }
          } else {
            // Nếu không có id thì tạo mới
            const newVer = this.versionRepo.create({
              product_id: id,
              name: ver.name,
              price: ver.price,
              stock: ver.stock,
              options: ver.options,
              sku_code: ver.sku_code,
            });
            await queryRunner.manager.save(newVer);
          }
        }
        // Xóa version nào không còn trong dto
        const dbVersions = await this.versionRepo.find({ where: { product_id: id } });
        const dtoIds = dto.product_versions.filter((v) => v.id).map((v) => v.id);
        const toDelete = dbVersions.filter((v) => !dtoIds.includes(v.id));
        if (toDelete.length > 0) {
          await queryRunner.manager.delete(
            ProductVersion,
            toDelete.map((v) => v.id),
          );
        }
      }

      await this.logClient.emit('log_queue', {
        action: 'product.update',
        user_id: userId,
        module: 'M2',
        description: `Cập nhật sản phẩm: "${savedProduct.name}"`,
        target_id: savedProduct.id,
        metadata: JSON.stringify(dto),
      });
      await queryRunner.commitTransaction();
      return savedProduct;
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw new InternalServerErrorException(err.message);
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Cập nhật images cho sản phẩm
   */
  async updateImages(id: string, imageIds: string[], userId: string): Promise<Product> {
    const product = await this.productRepo.findOne({ where: { id } });
    if (!product) throw new NotFoundException('Không tìm thấy sản phẩm');
    // Xóa hết ảnh cũ
    await this.imageRepo.delete({ product_id: id });
    // Lưu lại các ảnh mới
    const imageEntities = imageIds.map((imgId, idx) =>
      this.imageRepo.create({
        product_id: id,
        image_url: undefined, // FE cần truyền url nếu muốn lưu url, hoặc có thể lấy từ cloudinary
        image_id: imgId,
        display_order: idx,
      }),
    );
    await this.imageRepo.save(imageEntities);
    await this.logClient.emit('log_queue', {
      action: 'product.updateImages',
      user_id: userId,
      module: 'M2',
      description: `Cập nhật images cho sản phẩm: "${product.name}"`,
      target_id: product.id,
      metadata: JSON.stringify(imageIds),
    });
    return product;
  }

  /**
   * Cập nhật thuộc tính cho sản phẩm
   */
  async updateAttributes(
    id: string,
    attributes: UpdateProductAttributesDto[],
    userId: string,
  ): Promise<Product> {
    const product = await this.productRepo.findOne({ where: { id } });
    if (!product) throw new NotFoundException('Không tìm thấy sản phẩm');
    const queryRunner = this.productRepo.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      // Xóa hết thuộc tính cũ
      await queryRunner.manager.delete(ProductAttribute, { product_id: id });
      // Lưu lại thuộc tính mới
      if (attributes && Array.isArray(attributes)) {
        const attrEntities = attributes.map((attr) =>
          this.attributeRepo.create({
            product_id: id,
            name: attr.attribute_name,
            value: attr.value,
            unit: attr.unit,
            is_searchable: attr.is_searchable,
          }),
        );
        await queryRunner.manager.save(attrEntities);
      }
      await this.logClient.emit('log_queue', {
        action: 'product.updateAttributes',
        user_id: userId,
        module: 'M2',
        description: `Cập nhật thuộc tính cho sản phẩm: "${product.name}"`,
        target_id: product.id,
        metadata: JSON.stringify(attributes),
      });
      await queryRunner.commitTransaction();
      return product;
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Tắt (ẩn) sản phẩm
   */
  async disable(id: string, userId: string): Promise<Product> {
    const product = await this.productRepo.findOne({ where: { id } });
    if (!product) throw new NotFoundException('Không tìm thấy sản phẩm');
    product.is_public = false;
    // Lấy status_id của status HIDDEN từ bảng status
    const statusRepo = this.productRepo.manager.getRepository('Status');
    const hiddenStatus = await statusRepo.findOne({ where: { code: 'HIDDEN', type: 'PRODUCT' } });
    if (!hiddenStatus) throw new NotFoundException('Không tìm thấy trạng thái HIDDEN cho sản phẩm');
    product.status_id = hiddenStatus.id;
    const saved = await this.productRepo.save(product);
    await this.logClient.emit('log_queue', {
      action: 'product.disable',
      user_id: userId,
      module: 'M2',
      description: `Tắt (ẩn) sản phẩm: "${saved.name}"`,
      target_id: saved.id,
      metadata: JSON.stringify({ is_public: false, status_id: product.status_id }),
    });
    return saved;
  }

  /**
   * Xóa sản phẩm
   */
  async delete(id: string, userId: string): Promise<void> {
    const product = await this.productRepo.findOne({ where: { id } });
    if (!product) throw new NotFoundException('Không tìm thấy sản phẩm');
    await this.productRepo.delete(id);
    await this.logClient.emit('log_queue', {
      action: 'product.delete',
      user_id: userId,
      module: 'M2',
      description: `Xóa sản phẩm: "${product.name}"`,
      target_id: product.id,
      metadata: null,
    });
  }

  async findOneWithVersions(id: string): Promise<any> {
    const product = await this.productRepo.findOne({ where: { id } });
    if (!product) throw new NotFoundException('Không tìm thấy sản phẩm');
    const versions = await this.versionRepo.find({
      where: { product_id: id },
      select: ['id', 'name', 'price', 'stock', 'sku_code', 'options'],
      order: { created_at: 'ASC' }, // ✅ Sắp xếp theo thời gian tạo để giữ thứ tự ổn định
    });
    const attributes = await this.attributeRepo.find({
      where: { product_id: id },
      select: ['id', 'name', 'value', 'unit', 'is_searchable'],
    });
    const images = await this.imageRepo.find({
      where: { product_id: id },
      select: ['id', 'image_url', 'image_id', 'display_order'],
      order: { display_order: 'ASC' },
    });

    // QR code logic
    let qrCode = await this.qrCodeRepo.findOne({
      where: { product_id: id, type: ProductQrCodeType.DEFAULT },
    });
    if (!qrCode) {
      // Tạo mới QR code nếu chưa có
      let prefix = process.env.PRODUCT_QR_PREFIX;
      if (!prefix && this.configService) {
        prefix = this.configService.get<string>('PRODUCT_QR_PREFIX');
      }
      if (!prefix) {
        prefix = 'https://yourdomain.com/qr/';
      }
      const targetUrl = `${prefix}${id}`;
      qrCode = this.qrCodeRepo.create({
        product_id: id,
        qr_url: targetUrl, // Có thể là link ảnh QR hoặc link đích, tuỳ logic
        target_url: targetUrl,
        type: ProductQrCodeType.DEFAULT,
        scan_count: 0,
      });
      qrCode = await this.qrCodeRepo.save(qrCode);
    }

    return {
      ...product,
      product_versions: versions,
      product_attribute: attributes,
      product_images: images,
      product_qr_code: qrCode,
    };
  }

  /**
   * Lấy thông tin inventory cho một sản phẩm
   */
  async getProductInventory(productId: string, versionId?: string): Promise<any> {
    const product = await this.productRepo.findOne({ where: { id: productId } });
    if (!product) throw new NotFoundException('Không tìm thấy sản phẩm');

    const invRepo = this.productRepo.manager.getRepository('Inventory');

    if (versionId) {
      // Lấy inventory cho version cụ thể
      const inv = await invRepo.findOne({ where: { version_id: versionId } });
      if (!inv) {
        // Fallback: lấy stock từ product_version nếu không có inventory
        const version = await this.versionRepo.findOne({ where: { id: versionId } });
        const stockQuantity = version?.stock || 0;
        return {
          product_id: productId,
          version_id: versionId,
          available: stockQuantity,
          stock_quantity: stockQuantity,
          reserved_quantity: 0,
        };
      }
      return {
        product_id: productId,
        version_id: versionId,
        available: Math.max(0, inv.stock_quantity - inv.reserved_quantity),
        stock_quantity: inv.stock_quantity,
        reserved_quantity: inv.reserved_quantity,
      };
    } else {
      // Lấy inventory cho sản phẩm (không có version)
      const inv = await invRepo.findOne({ where: { product_id: productId, version_id: null } });
      if (!inv) {
        // Fallback: lấy stock từ product nếu không có inventory
        return {
          product_id: productId,
          version_id: null,
          available: product.stock || 0,
          stock_quantity: product.stock || 0,
          reserved_quantity: 0,
        };
      }
      return {
        product_id: productId,
        version_id: null,
        available: Math.max(0, inv.stock_quantity - inv.reserved_quantity),
        stock_quantity: inv.stock_quantity,
        reserved_quantity: inv.reserved_quantity,
      };
    }
  }

  /**
   * Lấy thống kê đánh giá và số lượng đã bán cho sản phẩm
   */
  async getProductStats(productId: string): Promise<any> {
    const product = await this.productRepo.findOne({ where: { id: productId } });
    if (!product) throw new NotFoundException('Không tìm thấy sản phẩm');

    // Kiểm tra xem có reviews nào không
    const totalReviewsCount = await this.productRepo.manager
      .getRepository('product_reviews')
      .count({
        where: {
          product_id: productId,
          is_public: true,
          parent_id: null,
        },
      });

    // Lấy tất cả đánh giá của sản phẩm với thông tin user và profile
    const reviews = await this.productRepo.manager.getRepository('product_reviews').find({
      where: {
        product_id: productId,
        is_public: true,
        parent_id: null,
      },
      relations: ['user', 'user.profile'],
      order: { created_at: 'DESC' },
    });

    // Debug: Kiểm tra dữ liệu

    // Nếu không có reviews, tạo dữ liệu test (chỉ trong development)
    if (reviews.length === 0 && process.env.NODE_ENV === 'development') {
      // Lấy user đầu tiên để tạo review test
      const testUser = await this.productRepo.manager.getRepository('users').findOne({
        where: {},
        relations: ['profile'],
        select: ['id', 'email'],
      });

      if (testUser) {
        // Tạo review test
        const testReview = await this.productRepo.manager.getRepository('product_reviews').save({
          product_id: productId,
          user_id: testUser.id,
          rating: 5,
          content: 'Sản phẩm rất tốt, giao hàng nhanh!',
          is_public: true,
          parent_id: null,
        });

        // Lấy lại review với thông tin user và profile
        const testReviewWithUser = await this.productRepo.manager
          .getRepository('product_reviews')
          .findOne({
            where: { id: testReview.id },
            relations: ['user', 'user.profile'],
          });

        if (testReviewWithUser) {
          reviews.push(testReviewWithUser);
        }
      }
    }

    // Tính toán thống kê đánh giá
    const totalReviews = reviews.length;
    const averageRating =
      totalReviews > 0 ? reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews : 0;

    // Lấy số lượng đã bán từ đơn hàng đã hoàn thành
    const completedStatus = await this.productRepo.manager
      .getRepository('statuses')
      .findOne({ where: { code: 'COMPLETED' } });

    let totalSold = 0;
    if (completedStatus) {
      const soldItems = await this.productRepo.manager
        .getRepository('order_items')
        .createQueryBuilder('oi')
        .leftJoin('oi.order', 'order')
        .where('oi.product_id = :productId', { productId })
        .andWhere('order.status_id = :statusId', { statusId: completedStatus.id })
        .select('SUM(oi.quantity)', 'total')
        .getRawOne();

      totalSold = parseInt(soldItems?.total || '0');
    }

    const result = {
      total_reviews: totalReviews,
      average_rating: Math.round(averageRating * 10) / 10, // Làm tròn 1 chữ số thập phân
      total_sold: totalSold,
      reviews: reviews.slice(0, 10), // Chỉ trả về 10 đánh giá gần nhất
    };

    return result;
  }

  async findAllWithVersions(query?: ProductListRequestDto): Promise<{
    page: number;
    size: number;
    total: number;
    totalPages: number;
    data: any[];
    error?: string;
  }> {
    try {
      let page = 1;
      let size = 20;
      if (query) {
        if (typeof query.page === 'string') query.page = parseInt(query.page, 10);
        if (typeof query.size === 'string') query.size = parseInt(query.size, 10);
        if (typeof query.page === 'number' && Number.isInteger(query.page) && query.page > 0)
          page = query.page;
        if (typeof query.size === 'number' && Number.isInteger(query.size) && query.size > 0)
          size = query.size;
      }
      const skip = (page - 1) * size;
      const take = size;
      const where: any = {};
      // Lấy categoryIds nếu có category_id (chỉ truyền 1 id)
      let categoryIds: string[] | undefined;
      if (query?.category_id) {
        categoryIds = await this.productCategoriesService.getAllDescendantCategoryIds(
          query.category_id,
        );
      }
      if (typeof query?.search === 'string' && query.search.trim() !== '') {
        const search = query.search.toLowerCase();
        // Sử dụng QueryBuilder cho tìm kiếm phức tạp
        const qb = this.productRepo.createQueryBuilder('product');
        qb.where('LOWER(product.name) LIKE :search OR LOWER(product.slug) LIKE :search', {
          search: `%${search}%`,
        });
        // Apply các filter khác nếu có
        if (categoryIds && categoryIds.length > 0)
          qb.andWhere('product.category_id IN (:...categoryIds)', { categoryIds });
        if (query?.user_id) qb.andWhere('product.user_id = :userId', { userId: query.user_id });
        if (query?.status_id)
          qb.andWhere('product.status_id = :status_id', { status_id: query.status_id });
        else if (query?.status)
          qb.andWhere('product.status_id = :status_id', { status_id: query.status });
        if (query?.source) qb.andWhere('product.source = :source', { source: query.source });
        if (typeof query?.price_min === 'number')
          qb.andWhere('product.price >= :priceMin', { priceMin: query.price_min });
        if (typeof query?.price_max === 'number')
          qb.andWhere('product.price <= :priceMax', { priceMax: query.price_max });
        if (query?.created_from)
          qb.andWhere('product.created_at >= :createdFrom', { createdFrom: query.created_from });
        if (query?.created_to)
          qb.andWhere('product.created_at <= :createdTo', { createdTo: query.created_to });
        if (typeof query?.has_stock === 'boolean') {
          if (query.has_stock) qb.andWhere('product.stock > 0');
          else qb.andWhere('product.stock <= 0');
        }
        // Sắp xếp
        if (query?.sort_by) {
          qb.orderBy(
            `product.${query.sort_by}`,
            (query?.sort_order || 'desc').toUpperCase() as any,
          );
        } else {
          qb.orderBy(
            `product.${query?.order_by || 'created_at'}`,
            (query?.order_dir || 'desc').toUpperCase() as any,
          );
        }
        qb.skip(skip).take(take);
        const [products, total] = await qb.getManyAndCount();
        const productIds = products.map((p) => p.id);
        if (productIds.length === 0) {
          return {
            page,
            size,
            total: 0,
            totalPages: 0,
            data: [],
          };
        }
        // ...lấy versions, attributes, images như cũ...
        const versions = await this.versionRepo
          .createQueryBuilder('v')
          .where('v.product_id IN (:...ids)', { ids: productIds })
          .select([
            'v.id',
            'v.product_id',
            'v.name',
            'v.price',
            'v.stock',
            'v.sku_code',
            'v.options',
          ])
          .orderBy('v.created_at', 'ASC') // ✅ Sắp xếp theo thời gian tạo
          .getMany();
        const attributes = await this.attributeRepo
          .createQueryBuilder('a')
          .where('a.product_id IN (:...ids)', { ids: productIds })
          .select(['a.id', 'a.product_id', 'a.name', 'a.value', 'a.unit', 'a.is_searchable'])
          .getMany();
        const images = await this.imageRepo
          .createQueryBuilder('i')
          .where('i.product_id IN (:...ids)', { ids: productIds })
          .select(['i.id', 'i.product_id', 'i.image_url', 'i.image_id', 'i.display_order'])
          .orderBy('i.display_order', 'ASC')
          .getMany();
        const versionMap = versions.reduce(
          (acc, v) => {
            if (!acc[v.product_id]) acc[v.product_id] = [];
            acc[v.product_id].push(v);
            return acc;
          },
          {} as Record<string, any[]>,
        );
        const attrMap = attributes.reduce(
          (acc, a) => {
            if (!acc[a.product_id]) acc[a.product_id] = [];
            acc[a.product_id].push(a);
            return acc;
          },
          {} as Record<string, any[]>,
        );
        const imageMap = images.reduce(
          (acc, i) => {
            if (!acc[i.product_id!]) acc[i.product_id!] = [];
            acc[i.product_id!].push(i);
            return acc;
          },
          {} as Record<string, any[]>,
        );
        return {
          page,
          size,
          total,
          totalPages: Math.ceil(total / size),
          data: products.map((p) => ({
            ...p,
            product_versions: versionMap[p.id] || [],
            product_attribute: attrMap[p.id] || [],
            product_images: imageMap[p.id] || [],
          })),
        };
      }
      // Lọc theo category (bao gồm cả con)
      // let categoryIds: string[] | undefined; // Đã khai báo phía trên, không cần lại
      if (query?.category_id) {
        categoryIds = await this.productCategoriesService.getAllDescendantCategoryIds(
          query.category_id,
        );
        where['category_id'] = In(categoryIds);
      }
      // Lọc theo user_id
      if (query?.user_id) {
        where['user_id'] = query.user_id;
      }
      // Lọc theo status
      if (query?.status_id) {
        where['status_id'] = query.status_id;
      } else if (query?.status) {
        where['status_id'] = query.status;
      }
      // Lọc theo source
      if (query?.source) {
        where['source'] = query.source;
      }
      // Lọc theo giá
      if (typeof query?.price_min === 'number') {
        where['price'] = where['price'] || {};
        where['price'] = (qb: any) => `${qb.alias}.price >= ${query.price_min}`;
      }
      if (typeof query?.price_max === 'number') {
        where['price'] = where['price'] || {};
        where['price'] = (qb: any) => `${qb.alias}.price <= ${query.price_max}`;
      }
      // Lọc theo ngày tạo
      if (query?.created_from) {
        where['updated_at'] = where['updated_at'] || {};
        where['updated_at'] = (qb: any) => `${qb.alias}.updated_at >= '${query.created_from}'`;
      }
      if (query?.created_to) {
        where['updated_at'] = where['updated_at'] || {};
        where['updated_at'] = (qb: any) => `${qb.alias}.updated_at <= '${query.created_to}'`;
      }
      // Lọc theo tồn kho
      if (typeof query?.has_stock === 'boolean') {
        if (query.has_stock) {
          where['stock'] = (qb: any) => `${qb.alias}.stock > 0`;
        } else {
          where['stock'] = (qb: any) => `${qb.alias}.stock <= 0`;
        }
      }
      // Sắp xếp
      const order: any = {};
      // Đảm bảo luôn prefix alias 'product.' cho các trường order_by/sort_by
      if (query?.sort_by) {
        const sortField = query.sort_by.includes('.') ? query.sort_by : `product.${query.sort_by}`;
        order[sortField] = (query?.sort_order || 'desc').toUpperCase();
      } else {
        const orderField = query?.order_by
          ? query.order_by.includes('.')
            ? query.order_by
            : `product.${query.order_by}`
          : 'product.updated_at';
        order[orderField] = (query?.order_dir || 'desc').toUpperCase();
      }
      // Lấy sản phẩm
      // Join với bảng status để lấy label
      const qb = this.productRepo
        .createQueryBuilder('product')
        .leftJoinAndSelect('product.status', 'status', "status.type = 'PRODUCT'")
        .where(where)
        .orderBy(order)
        .skip(skip)
        .take(take);
      const [products, total] = await qb.getManyAndCount();
      const productIds = products.map((p) => p.id);
      if (productIds.length === 0) {
        return {
          page,
          size,
          total: 0,
          totalPages: 0,
          data: [],
        };
      }
      const versions = await this.versionRepo
        .createQueryBuilder('v')
        .where('v.product_id IN (:...ids)', { ids: productIds })
        .select(['v.id', 'v.product_id', 'v.name', 'v.price', 'v.stock', 'v.sku_code', 'v.options'])
        .orderBy('v.created_at', 'ASC') // ✅ Sắp xếp theo thời gian tạo
        .getMany();
      const attributes = await this.attributeRepo
        .createQueryBuilder('a')
        .where('a.product_id IN (:...ids)', { ids: productIds })
        .select(['a.id', 'a.product_id', 'a.name', 'a.value', 'a.unit', 'a.is_searchable'])
        .getMany();
      const images = await this.imageRepo
        .createQueryBuilder('i')
        .where('i.product_id IN (:...ids)', { ids: productIds })
        .select(['i.id', 'i.product_id', 'i.image_url', 'i.image_id', 'i.display_order'])
        .orderBy('i.display_order', 'ASC')
        .getMany();
      const versionMap = versions.reduce(
        (acc, v) => {
          if (!acc[v.product_id]) acc[v.product_id] = [];
          acc[v.product_id].push(v);
          return acc;
        },
        {} as Record<string, any[]>,
      );
      const attrMap = attributes.reduce(
        (acc, a) => {
          if (!acc[a.product_id]) acc[a.product_id] = [];
          acc[a.product_id].push(a);
          return acc;
        },
        {} as Record<string, any[]>,
      );
      const imageMap = images.reduce(
        (acc, i) => {
          if (!acc[i.product_id!]) acc[i.product_id!] = [];
          acc[i.product_id!].push(i);
          return acc;
        },
        {} as Record<string, any[]>,
      );
      const totalPages = Math.ceil(total / size);
      return {
        page,
        size,
        total,
        totalPages,
        data: products.map((p) => {
          const versions = versionMap[p.id] || [];
          let stock = 0;
          if (versions.length > 0) {
            stock = versions.reduce((sum, v) => sum + (v.stock ?? 0), 0);
          } else {
            stock = p.stock ?? 0;
          }
          return {
            ...p,
            stock,
            status_label: p.status?.label || null,
            status_label_color: p.status?.color || null,
            product_versions: versions,
            product_attribute: attrMap[p.id] || [],
            product_images: imageMap[p.id] || [],
          };
        }),
      };
    } catch (error) {
      console.error('Lỗi khi truy vấn danh sách sản phẩm:', error);
      throw new InternalServerErrorException(
        'Đã xảy ra lỗi khi truy vấn danh sách sản phẩm. Vui lòng kiểm tra lại tham số hoặc liên hệ quản trị viên.',
      );
    }
  }

  /**
   * Tạo tồn kho cho các version (SKU) của sản phẩm
   */
  private async _createInventoryForProductVersions(
    productId: string,
    versions: any[],
    manager: any,
  ) {
    if (!versions || versions.length === 0) return;
    const inventoryRepo = manager.getRepository('Inventory');
    for (const ver of versions) {
      // Nếu đã có inventory cho version này thì bỏ qua
      const existed = await inventoryRepo.findOne({ where: { version_id: ver.id } });
      if (!existed) {
        await inventoryRepo.save({
          product_id: productId,
          version_id: ver.id,
          stock_quantity: ver.stock ?? 0,
          reserved_quantity: 0,
        });
      }
    }
  }

  /**
   * Tìm kiếm sản phẩm bằng hình ảnh (AI object detection)
   */
  /**
   * Ánh xạ class AI sang category_id. Nếu thiếu class sẽ throw lỗi.
   */
  private mapAIClassToCategoryId(aiClass: string): string {
    const mapping: Record<string, string> = {
      'trang-suc': 'df7c4-d83b-4576-a3e4-d9caa7b82f36',
      'vali-balo-phu-kien-du-lich': 'ecf94f19-09b4-4686-ba2a-7ca64a78f9ae',
      camera: '89c552f4-348c-4388-b7a3-20b2a2d660ce',
      'dien-thoai': '11145114-110e-422f-b77d-1e169002029a',
      laptop: 'c2cb5982-4747-4c94-9b0e-a996b63f9059',
      'linh-kien-may-tinh': '45bc9e4c-ee27-4299-983b-26d4a1417ec4',
      loa: 'f6338543-bd75-4ec2-a634-c01b1e289b5c',
      'may-tinh': 'c800b127-a302-4a19-8925-69191efe4278',
      'may-tinh-bang': '564bb9c0-34ed-4895-bf6e-ef6c5afbe68a',
      'phu-kien-dien-thoai': '1ef6c58e-7f5f-4451-8ce3-d23a4a2e5676',
      'tai-nghe': '620f74f3-9f28-4714-b1f3-b870ddd3276c',
      'thiet-bi-deo-thong-minh': '30612a64-8ad0-4f4c-ad1a-2673b9725230',
      'thiet-bi-dien-tu-cho-xe': '056bec49-e969-46f4-8679-5697f85b9bc6',
      'chan-ga-goi': 'a74959c5-229d-469b-bd49-33bd6edcc996',
      'do-dien-gia-dung': 'e66f089f-191e-40ac-98ca-5a2e7123f70b',
      'do-dung-cam-trai-da-ngoai': '3631a5c5-4347-4a03-bc48-74f03546daf3',
      'do-dung-nha-bep': 'b6c5f276-5ad5-48a9-907f-939c2addb322',
      'do-thu-cong-my-nghe': 'a7c0ea2e-7353-48b8-8cad-895ff209c078',
      'dung_cu_hoc_sinh_&_van_phong_pham': '3559d661-9003-4669-bdab-bf1d15cc1529',
      'dung-cu-sua-chua': '32aacdd7-35e3-418e-93ce-46671401dc51',
      'dung-cu-the-thao': '62303ef1-9ee2-4fea-88e8-bb08377b7b4d',
      'san-pham-giat-giu-cham-soc-nha-cua': '0786a488-8d3a-4457-9cdf-3e998c108f9d',
      'sua-tam-dau-goi': '4dc91f6e-38fc-468d-817b-dfd7504add5c',
      'phu-tung-dau-nhot': '78de1119-a3fa-4afe-bef6-4b00a469cc5d',
      'thoi-trang': '87f997ac-5119-46b6-b76e-9645391f52ee',
      'thoi-trang-nam': 'd49ce340-7650-49d3-87e5-61540671d2fe',
      'giay-dep': '580e6730-15a2-44ca-929d-0c4312be8cd9',
      'thoi-trang-nu': '782e55b7-4c86-4b1f-95d8-659cf70a8a65',
      'mu-bao-hiem-do-bao-ho': '8173afdb-0791-41c8-b729-5596135ff249',
      'trang-phuc-giay-the-thao': '85a9067c-5a1a-40e8-9307-ab5e3783afa4',
      'tui-vi': '673f683d-0ec2-4a46-82bd-be834dfa0a0e',
      'thoi-trang-tre-em': 'f8b0fedf-3228-4154-807d-34ee418f2207',
      'tap-chi-truyen-tranh': '1cc7b455-7d3f-4c15-a598-57f2f017d727',
      san_pham_giat_giu_cham_soc_nha_cua: '0786a488-8d3a-4457-9cdf-3e998c108f9d',
      dien_thoai: '11145114-110e-422f-b77d-1e169002029a',
      'thuc_pham_kho_&_dong_hop': 'be003e68-3ac3-4026-b02f-3cbddfdfcfd0',
      thuc_pham_tuoi_song: '84afed53-502d-4606-a30f-6d0f8692ec8f',
      'do_an_vat_&_banh_keo': 'f1f49908-10d8-4b79-8f76-afcb05c90a48',
      thoi_trang_nu: '782e55b7-4c86-4b1f-95d8-659cf70a8a65',
      do_uong: 'c87a9592-f448-4507-bdd1-eb85f5f9616a',
      phu_kien_dien_thoai: '1ef6c58e-7f5f-4451-8ce3-d23a4a2e5676',
      thiet_bi_deo_thong_minh: '30612a64-8ad0-4f4c-ad1a-2673b9725230',
      'tap_chi_&_truyen_tranh': '1cc7b455-7d3f-4c15-a598-57f2f017d727',
    };
    if (!mapping[aiClass]) {
      throw new BadRequestException(`Không tìm thấy mapping cho class AI: ${aiClass}`);
    }
    return mapping[aiClass];
  }

  /**
   * Tìm kiếm sản phẩm bằng hình ảnh (AI object detection)
   */
  async searchProductByImageCategory(file: Express.Multer.File) {
    if (!file) return { error: 'No image uploaded' };
    const FormData = require('form-data');
    const form = new FormData();
    form.append('file', file.buffer, { filename: file.originalname });
    try {
      console.log('Connecting to object detection service...');

      const aiServiceBase = process.env.OBJECT_DETECTION_URL || 'http://localhost:8003';
      const aiServiceUrl = `${aiServiceBase}/detect`;
      const response = await axios.post(aiServiceUrl, form, {
        headers: form.getHeaders(),
        maxContentLength: Infinity,
        maxBodyLength: Infinity,
      });
      console.log('response', response.data);
      const categories = response.data.categories;
      if (!categories || categories.length === 0) {
        return { products: [], detectedCategories: [] };
      }
      // Map tất cả class AI sang category_id và lấy name
      const detectedCategories: { class: string; name: string }[] = [];
      for (const aiClass of categories) {
        try {
          const categoryId = this.mapAIClassToCategoryId(aiClass);
          const cat = await this.productCategoriesService.findOne(categoryId);
          console.log('cat', cat?.id);
          detectedCategories.push({ class: aiClass, name: cat?.name || aiClass });
        } catch (e) {
          detectedCategories.push({ class: aiClass, name: aiClass });
        }
      }
      // Lấy sản phẩm theo category đầu tiên
      const firstCategoryId = this.mapAIClassToCategoryId(categories[0]);
      const products = await this.findAllWithVersions({ search: '', category_id: firstCategoryId });
      return { products, detectedCategories };
    } catch (err) {
      return { error: 'Object detection service error', detail: err?.message };
    }
  }
}
