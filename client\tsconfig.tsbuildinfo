{"root": ["./src/app.tsx", "./src/main.tsx", "./src/components/button/index.tsx", "./src/components/chatbot/chatbotwidget.tsx", "./src/components/confirmmodal/index.tsx", "./src/components/forminput/index.tsx", "./src/components/modalbuttongroup/index.tsx", "./src/components/notification/notificationmodal.tsx", "./src/components/profilemodals/emailotpmodal.tsx", "./src/components/profilemodals/phoneotpmodal.tsx", "./src/components/profilemodals/resetpasswordmodal.tsx", "./src/components/responsiveimage/index.tsx", "./src/components/toast/index.tsx", "./src/components/home/<USER>/footer.tsx", "./src/components/home/<USER>/header.tsx", "./src/components/home/<USER>/herobanner.tsx", "./src/components/home/<USER>/productcard.tsx", "./src/components/home/<USER>/productgrid.tsx", "./src/constants/permissions.ts", "./src/constants/routes.ts", "./src/constants/sidebaritems.tsx", "./src/helpers/protectedroutes.tsx", "./src/helpers/permission.ts", "./src/hooks/affiliate/useaffiliate.tsx", "./src/hooks/auth/uselogin.ts", "./src/hooks/auth/useregister.ts", "./src/hooks/auth/useresetpassword.ts", "./src/hooks/cart/usecart.tsx", "./src/hooks/chatbot/chatbot.ts", "./src/hooks/inventory/useinventory.tsx", "./src/hooks/orders/useorder.tsx", "./src/hooks/orders/useordermanagement.tsx", "./src/hooks/product/usecategory.tsx", "./src/hooks/product/useproductreview.tsx", "./src/hooks/product/useproduct.tsx", "./src/hooks/profile/useprofile.ts", "./src/hooks/profile/qr/useqr.ts", "./src/hooks/system/useusermanagement.ts", "./src/hooks/user/index.tsx", "./src/layouts/commercelayout.tsx", "./src/layouts/protectedlayout.tsx", "./src/layouts/publiclayout.tsx", "./src/layouts/homepagelayout/index.tsx", "./src/layouts/homepagelayout/footer/index.tsx", "./src/layouts/homepagelayout/navbar/index.tsx", "./src/layouts/nulllayout/index.tsx", "./src/layouts/systemlayout/index.tsx", "./src/layouts/systemlayout/drawer/index.tsx", "./src/layouts/systemlayout/footer/index.tsx", "./src/layouts/systemlayout/header/index.tsx", "./src/layouts/systemlayout/sidebar/index.tsx", "./src/libs/utils.ts", "./src/libs/axios/axiosauth.ts", "./src/libs/axios/axiosbase.ts", "./src/libs/axios/axiosoptional.ts", "./src/libs/axios/axiospublic.ts", "./src/libs/features/auth/authslice.ts", "./src/libs/features/auth/type.ts", "./src/libs/state/store.ts", "./src/pages/commerce/commerce.tsx", "./src/pages/error/authorize.tsx", "./src/pages/error/network.tsx", "./src/pages/error/notfound.tsx", "./src/pages/home/<USER>", "./src/pages/home/<USER>", "./src/pages/home/<USER>", "./src/pages/home/<USER>", "./src/pages/home/<USER>", "./src/pages/home/<USER>", "./src/pages/home/<USER>", "./src/pages/m1/dashboard.tsx", "./src/pages/m1/auth/login.tsx", "./src/pages/m1/auth/register.tsx", "./src/pages/m1/resetpassword/changepassword.tsx", "./src/pages/m1/resetpassword/otpresetpassword.tsx", "./src/pages/m1/resetpassword/resetpassword.tsx", "./src/pages/m1/userprofile/profile/profileupdateform.tsx", "./src/pages/m1/userprofile/profile/qr.tsx", "./src/pages/m1/userprofile/profile/uploadavatar.tsx", "./src/pages/m1/userprofile/profile/index.tsx", "./src/pages/m1/usersmanagement/index.tsx", "./src/pages/m2/cart/cartguest.tsx", "./src/pages/m2/cart/cartitemdetail.tsx", "./src/pages/m2/cart/protectedcart.tsx", "./src/pages/m2/cart/smartcart.tsx", "./src/pages/m2/cart/index.tsx", "./src/pages/m2/category/index.tsx", "./src/pages/m2/categorymanagement/index.tsx", "./src/pages/m2/favorites/protectedfavorites.tsx", "./src/pages/m2/favorites/index.tsx", "./src/pages/m2/inventorymanagement/index.tsx", "./src/pages/m2/oders/protectedmyorders.tsx", "./src/pages/m2/oders/protectedorderdetail.tsx", "./src/pages/m2/oders/index.tsx", "./src/pages/m2/oders/myorder.tsx", "./src/pages/m2/ordermanagement/index.tsx", "./src/pages/m2/orderstatistics/index.tsx", "./src/pages/m2/productdetail/productdetailguest.tsx", "./src/pages/m2/productdetail/protectedproductdetail.tsx", "./src/pages/m2/productdetail/smartproductdetail.tsx", "./src/pages/m2/productdetail/index.tsx", "./src/pages/m2/productmanagement/index.tsx", "./src/private/cookies.ts", "./src/routes/index.tsx", "./src/services/chatbot/supportchatbot.ts", "./src/services/m1/affiliate.ts", "./src/services/m1/auth.ts", "./src/services/m1/user.ts", "./src/services/m1/qr/qr.ts", "./src/services/m1/usermanagement/usermanagement.ts", "./src/services/m2/cartmanagement/cart.ts", "./src/services/m2/inventorymanagement/inventorymanagement.ts", "./src/services/m2/ordermanagement/ordermanagement.ts", "./src/services/m2/orders/order.ts", "./src/services/m2/productmanagement/categorymanagement.ts", "./src/services/m2/productmanagement/productmanagement.ts", "./src/services/m2/productmanagement/productreview.ts", "./src/types/components.d.ts", "./src/types/facebook.d.ts", "./src/types/images.d.ts", "./src/types/user.type.ts", "./src/types/components/modal.ts", "./src/types/components/route.ts", "./src/utils/avatar.ts", "./src/utils/formatdate.ts", "./src/utils/loadingstore.ts", "./src/utils/permissionutils.ts"], "version": "5.8.3"}