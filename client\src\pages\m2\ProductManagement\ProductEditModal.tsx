// ...removed duplicate React import...
import {
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  TreeSelect,
  Upload,
  Button,
  Row,
  Col,
  Checkbox,
  Card,
} from 'antd'
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons'
import ModalButtonGroup from '@/components/ModalButtonGroup'
import type { CategoryItem, ProductImage } from '@/services/M2/ProductManagement/productManagement'
import styles from './ProductManagement.module.scss'

const { Option } = Select
const { TextArea } = Input

interface ProductEditModalProps {
  open: boolean
  onCancel: () => void
  form: any
  treeCategories: any[]
  categoriesLoading: boolean
  productStatuses: any[]
  editingProduct: any
  handleDisable: (id: string) => void
}

import React, { useState, useRef, useEffect } from 'react'
import * as productHooks from '@/hooks/product/useproduct'

const ProductEditModal: React.FC<ProductEditModalProps> = ({
  open,
  onCancel,
  form,
  treeCategories,
  categoriesLoading,
  productStatuses,
  editingProduct,
  handleDisable,
}) => {
  const [fileList, setFileList] = useState<any[]>([])
  const [uploading, setUploading] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [uploadedImages, setUploadedImages] = useState<any[]>([])
  const prevFileListLength = useRef(0)
  const uploadImages = productHooks.useUploadProductImagesHook()
  const updateProductWithImages = productHooks.useUpdateProductWithImagesHook()

  // Sync fileList with editingProduct when modal opens
  useEffect(() => {
    if (open && editingProduct) {
      const imgs = (editingProduct.product_images || []).map((img: any, idx: number) => ({
        uid: img.image_id || img.image_url || `image-${idx}`,
        name: img.image_id || img.image_url || `image-${idx}`,
        status: 'done',
        url: img.image_url || '',
        image_url: img.image_url || '',
        public_image_id: img.image_id || '',
        is_thumbnail: img.is_thumbnail ?? idx === 0,
        uploaded: true,
      }))
      setFileList(imgs)
      setUploadedImages(imgs)
      form.setFieldsValue({ product_images: imgs })
    }
    if (!open) {
      setFileList([])
      setUploadedImages([])
      form.setFieldsValue({ product_images: [] })
    }
  }, [open, editingProduct])

  const handleImageChange = async (info: any) => {
    const prevLen = prevFileListLength.current
    const currLen = info.fileList.length
    prevFileListLength.current = currLen
    if (currLen > prevLen) {
      const newFiles = info.fileList.filter(
        (f: any) => f.originFileObj && !f.uploaded && f.status !== 'uploading'
      )
      if (newFiles.length > 0) {
        let updatedFileList = info.fileList.map((f: any) => {
          if (newFiles.some((nf: any) => nf.uid === f.uid)) {
            return {
              ...f,
              status: 'uploading',
              name: f.name || (f.originFileObj ? f.originFileObj.name : f.uid),
            }
          }
          return { ...f, name: f.name || (f.originFileObj ? f.originFileObj.name : f.uid) }
        })
        setFileList(updatedFileList)
        setUploading(true)
        try {
          const files = newFiles.map((f: any) => f.originFileObj)
          const result = await uploadImages(files)
          // Map kết quả upload thành object chuẩn AntD Upload
          const mapped = (result || []).map((img: any, idx: number) => ({
            uid: img.public_image_id || img.image_url || `uploaded-${Date.now()}-${idx}`,
            name: img.public_image_id || img.image_url || `uploaded-${Date.now()}-${idx}`,
            status: 'done',
            url: img.image_url,
            image_url: img.image_url,
            public_image_id: img.public_image_id,
            is_thumbnail: img.is_thumbnail,
            uploaded: true,
          }))
          // Gộp fileList cũ (trừ file đang upload) + mapped mới
          updatedFileList = [
            ...updatedFileList.filter((f: any) => f.status !== 'uploading'),
            ...mapped,
          ]
          setFileList(updatedFileList)
          setUploadedImages(updatedFileList)
          form.setFieldsValue({ product_images: updatedFileList })
        } finally {
          setUploading(false)
        }
      }
    } else {
      // Đảm bảo name luôn là string cho mọi file
      const newList = info.fileList.map((f: any) => ({
        ...f,
        name: f.name || (f.originFileObj ? f.originFileObj.name : f.uid),
      }))
      setFileList(newList)
      setUploadedImages(newList)
      form.setFieldsValue({ product_images: newList })
    }
  }

  const handleRemove = (file: any) => {
    const newFileList = fileList.filter((f) => f.uid !== file.uid)
    setFileList(newFileList)
    setUploadedImages(newFileList)
    form.setFieldsValue({ product_images: newFileList })
  }

  const beforeUpload = (file: any) => {
    const isImage = file.type.startsWith('image/')
    const isLt5M = file.size / 1024 / 1024 < 5
    if (!isImage) {
      // @ts-ignore
      window.message?.error?.('Chỉ được chọn file ảnh!')
      return Upload.LIST_IGNORE
    }
    if (!isLt5M) {
      // @ts-ignore
      window.message?.error?.('Ảnh phải nhỏ hơn 5MB!')
      return Upload.LIST_IGNORE
    }
    return false
  }

  const handleSubmit = async (values: any) => {
    if (isSubmitting || uploading) {
      // @ts-ignore
      window.message?.warning?.('Vui lòng chờ upload ảnh xong!')
      return
    }
    setIsSubmitting(true)
    try {
      // Lấy product_images trực tiếp từ state fileList để luôn đồng bộ UI
      const currentFileList = fileList || []

      // Mapping đúng chuẩn DTO BE: public_image_id, image_url, is_thumbnail
      let allImages = currentFileList
        .filter(
          (img: any) =>
            img.status === 'done' &&
            (img.public_image_id || img.image_id || img.uid) &&
            (img.image_url || img.url)
        )
        .map((img: any, idx: number) => ({
          public_image_id: img.public_image_id || img.image_id || img.uid || '',
          image_url: img.image_url || img.url,
          is_thumbnail: img.is_thumbnail ?? idx === 0,
        }))

      // So sánh với ảnh gốc của editingProduct để xác định có thay đổi ảnh không
      const originalImages = (editingProduct?.product_images || []).map((img: any) => ({
        public_image_id: img.image_id || '',
        image_url: img.image_url || '',
        is_thumbnail: !!img.is_thumbnail,
      }))
      const isImagesChanged =
        allImages.length !== originalImages.length ||
        allImages.some(
          (img, idx) =>
            img.public_image_id !== (originalImages[idx]?.public_image_id || '') ||
            img.image_url !== (originalImages[idx]?.image_url || '') ||
            Boolean(img.is_thumbnail) !== Boolean(originalImages[idx]?.is_thumbnail)
        )
      // Nếu có thay đổi thì gửi product_images, nếu không thì loại khỏi payload
      const productData = {
        ...values,
        price: Number(values.price),
        ...(isImagesChanged ? { product_images: allImages } : {}),
      }
      let success = false
      success = await updateProductWithImages(editingProduct.id, productData)
      if (success) {
        onCancel()
        form.resetFields()
        setFileList([])
        setUploadedImages([])
      }
    } catch (error) {
      // @ts-ignore
      window.message?.error?.('Có lỗi xảy ra!')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Modal
      title="Chỉnh sửa sản phẩm"
      open={open}
      onCancel={onCancel}
      footer={null}
      width={900}
      className={styles.productModal}
      maskClosable={false}
      keyboard={false}
    >
      <Form form={form} onFinish={handleSubmit} layout="vertical" className={styles.productForm}>
        <Row gutter={16}>
          <Col xs={24} sm={12}>
            <Form.Item
              name="name"
              label="Tên sản phẩm"
              rules={[{ required: true, message: 'Vui lòng nhập tên sản phẩm!' }]}
            >
              <Input placeholder="Nhập tên sản phẩm" />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="unit"
              label="Đơn vị"
              rules={[{ required: true, message: 'Vui lòng nhập đơn vị!' }]}
            >
              <Input placeholder="kg, cái, hộp..." />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col xs={24} sm={12}>
            <Form.Item
              name="category_id"
              label="Danh mục"
              rules={[{ required: true, message: 'Vui lòng chọn danh mục!' }]}
            >
              <TreeSelect
                style={{ width: '100%' }}
                dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                treeData={treeCategories}
                placeholder="Chọn danh mục"
                treeDefaultExpandAll
                loading={categoriesLoading}
                showSearch
                treeNodeFilterProp="title"
                allowClear
                notFoundContent={categoriesLoading ? 'Đang tải...' : 'Không có danh mục'}
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="price"
              label="Giá bán"
              rules={[{ required: true, message: 'Vui lòng nhập giá bán!' }]}
            >
              <InputNumber
                min={0}
                placeholder="Nhập giá bán"
                style={{ width: '100%' }}
                formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col xs={24} sm={12}>
            <Form.Item name="status_id" label="Trạng thái">
              <Select placeholder="Chọn trạng thái" loading={!productStatuses} allowClear>
                {Array.isArray(productStatuses) &&
                  productStatuses.map((status) => (
                    <Option key={status.id} value={status.id}>
                      {status.label}
                    </Option>
                  ))}
              </Select>
            </Form.Item>
          </Col>
          <Col xs={24} sm={12} style={{ display: 'flex', alignItems: 'center', marginTop: 30 }}>
            <Form.Item name="is_public" valuePropName="checked" style={{ marginBottom: 0 }}>
              <Checkbox>Công khai</Checkbox>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item name="description" label="Mô tả sản phẩm">
          <TextArea rows={3} placeholder="Nhập mô tả sản phẩm" />
        </Form.Item>

        <Form.Item
          name="product_images"
          label="Hình ảnh sản phẩm"
          extra={`Đã chọn ${fileList.length} ảnh. Tối đa 10 ảnh, mỗi ảnh 5MB.`}
        >
          <Upload
            fileList={fileList}
            listType="picture-card"
            multiple
            maxCount={10}
            accept="image/*"
            beforeUpload={beforeUpload}
            onRemove={handleRemove}
            onChange={handleImageChange}
            disabled={isSubmitting || uploading}
          >
            <div>
              <PlusOutlined />
              <div style={{ marginTop: 8 }}>Chọn ảnh</div>
            </div>
          </Upload>
        </Form.Item>

        <Form.List name="product_attribute">
          {(fields, { add, remove }) => (
            <>
              <Form.Item label="Thuộc tính sản phẩm (Tùy chọn)">
                <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                  Thêm thuộc tính
                </Button>
              </Form.Item>
              {fields.map(({ key, name, ...restField }) => (
                <Row key={key} gutter={16} align="middle">
                  <Col span={6}>
                    <Form.Item {...restField} name={[name, 'attribute_name']}>
                      <Input placeholder="Tên thuộc tính" />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item {...restField} name={[name, 'value']}>
                      <Input placeholder="Giá trị" />
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    <Form.Item {...restField} name={[name, 'unit']}>
                      <Input placeholder="Đơn vị" />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      {...restField}
                      name={[name, 'is_searchable']}
                      valuePropName="checked"
                    >
                      <Checkbox>Có thể tìm kiếm</Checkbox>
                    </Form.Item>
                  </Col>
                  <Col span={2}>
                    <MinusCircleOutlined onClick={() => remove(name)} />
                  </Col>
                </Row>
              ))}
            </>
          )}
        </Form.List>

        <Form.List name="product_versions">
          {(fields, { add, remove }) => (
            <>
              <Form.Item label="Phiên bản sản phẩm (Tùy chọn)">
                <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                  Thêm phiên bản
                </Button>
              </Form.Item>
              {fields.map(({ key, name, ...restField }) => (
                <Card key={key} size="small" style={{ marginBottom: 16 }}>
                  <Row gutter={16}>
                    <Col xs={24} sm={12}>
                      <Form.Item {...restField} name={[name, 'name']} label="Tên phiên bản">
                        <Input placeholder="Phiên bản tiêu chuẩn" />
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={12}>
                      <Form.Item {...restField} name={[name, 'sku_code']} label="Mã SKU">
                        <Input placeholder="SKU-001" />
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={8}>
                      <Form.Item {...restField} name={[name, 'price']} label="Giá bán">
                        <InputNumber min={0} placeholder="0" style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={8}>
                      <Form.Item {...restField} name={[name, 'stock']} label="Tồn kho">
                        <InputNumber min={0} placeholder="0" style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={8}>
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                        }}
                      >
                        <span>Tùy chọn</span>
                        <Button
                          variant="outlined"
                          icon={<MinusCircleOutlined />}
                          onClick={() => remove(name)}
                          size="small"
                        >
                          Xóa
                        </Button>
                      </div>
                    </Col>
                  </Row>
                </Card>
              ))}
            </>
          )}
        </Form.List>

        <Form.Item>
          <ModalButtonGroup
            onCancel={onCancel}
            onConfirm={() => form.submit()}
            onSecondary={
              editingProduct
                ? async () => {
                    if (editingProduct) {
                      await handleDisable(editingProduct.id)
                      onCancel()
                    }
                  }
                : undefined
            }
            cancelText="Hủy bỏ"
            confirmText={isSubmitting || uploading ? 'Đang xử lý...' : 'Cập nhật'}
            secondaryText="Ẩn sản phẩm"
            confirmLoading={isSubmitting || uploading}
            disabled={isSubmitting || uploading}
            showSecondary={!!editingProduct}
            confirmVariant="primary"
            secondaryVariant="warning"
          />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default ProductEditModal
